#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警监控系统 - PySide6版本
只需要这一个文件即可运行
"""

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警监控系统 - PySide6版本 (完整优化版)
集成所有功能和优化，单文件部署
"""

import sys
import sqlite3
import json
from datetime import datetime
import os
import configparser
import random
import base64
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

# ================================
# 常量配置 (集成到主文件)
# ================================

# 数据库配置
DB_FILE = "zte_alarms.db"
DB_TIMEOUT = 5.0
DB_QUERY_LIMIT = 500

# 刷新配置
FETCH_TIMEOUT = 300  # 秒
AUTO_REFRESH_INTERVAL = 10000  # 实时监控刷新间隔（毫秒）- 10秒

# 重点关注告警关键字（默认值，实际使用时从配置文件加载）
DEFAULT_FOCUS_KEYWORDS = [
    "小区退",
    "天馈驻波比异常",
    "网元链路断"
]

# 网管系统配置
DEFAULT_WEB_URL = "https://*************:28001"


# UI配置
WINDOW_WIDTH = 1600
WINDOW_HEIGHT = 900
LOG_MAX_LINES = 1000
TABLE_BATCH_CHECK_INTERVAL = 100  # 每处理多少行检查一次停止请求

# 表格列配置 - 完整的203个原始字段映射
TABLE_COLUMNS = [
    # 基础显示列（0-10）- 核心字段
    "状态",           # 0 - 计算字段（is_new, is_baseline等）
    "重点标记",       # 1 - 计算字段（重点关注标记）
    "关联标记",       # 2 - raw_data.relationflagname（从第86列移动过来）
    "告警名称",       # 3 - code_name / raw_data.codename
    "根源ID",        # 4 - 根源分组唯一ID（短ID显示，Tooltip显示完整ID）
    "级别",           # 5 - perceived_severity_name / raw_data.perceivedseverityname
    "网元名称",       # 6 - me_name / raw_data.mename
    "IP地址",         # 7 - ne_ip / raw_data.neip
    "发生时间",       # 8 - alarm_raised_time / raw_data.alarmraisedtime
    "考核持续时间",   # 9 - effective_duration_minutes
    "运营商",         # 10 - 计算字段（根据PLMN显示信息判断）

    # 数据库字段（10-28）
    "确认状态",       # 10 - ack_state_name / raw_data.ackstatename
    "告警类型",       # 11 - alarm_type_name / raw_data.alarmtypename
    "原因",           # 12 - reason_name / raw_data.reasonname
    "附加信息",       # 13 - additional_text / raw_data.additionaltext
    "位置信息",       # 14 - position_name / raw_data.positionname
    "告警代码",       # 15 - alarm_code / raw_data.alarmcode
    "资源类型",       # 16 - res_type_name / raw_data.restypename
    "清除状态",       # 17 - clear_state_name / raw_data.clearstatename
    "确认用户",       # 18 - ack_user_id / raw_data.ackuserid
    "备注",           # 19 - comment_text / raw_data.commenttext
    "首次发现",       # 20 - first_seen_at
    "最后发现",       # 21 - last_seen_at
    "告警键",         # 22 - alarm_key / raw_data.alarmkey
    "是否新告警",     # 23 - is_new
    "是否活跃",       # 24 - is_active
    "状态变更时间",   # 25 - status_changed_at
    "创建时间",       # 26 - created_at
    "实际持续时间",   # 27 - actual_duration_minutes
    "是否基线",       # 28 - is_baseline

    # 基础告警字段组（27-56）
    "S-NSSAI",        # 27 - raw_data.S-NSSAI
    "告警ID",         # 28 - raw_data.id
    "确认信息",       # 29 - raw_data.ackinfo
    "确认状态码",     # 30 - raw_data.ackstate
    "确认系统ID",     # 31 - raw_data.acksystemid
    "确认时间",       # 32 - raw_data.acktime
    "确认用户ID",     # 33 - raw_data.ackuserid
    "管理域",         # 34 - raw_data.admc
    "管理域名称",     # 35 - raw_data.admcname
    "AID",            # 36 - raw_data.aid
    "告警变更时间",   # 37 - raw_data.alarmchangedtime
    "告警代码",       # 38 - raw_data.alarmcode
    "告警键",         # 39 - raw_data.alarmkey
    "告警发生时间",   # 40 - raw_data.alarmraisedtime
    "告警源",         # 41 - raw_data.alarmsource
    "告警标题",       # 42 - raw_data.alarmtitle
    "告警类型码",     # 43 - raw_data.alarmtype
    "告警类型名",     # 44 - raw_data.alarmtypename
    "辅助计数",       # 45 - raw_data.auxiliarycount
    "清除状态码",     # 46 - raw_data.clearstate
    "清除状态名",     # 47 - raw_data.clearstatename
    "清除类型名",     # 48 - raw_data.cleartypename
    "告警名称",       # 49 - raw_data.codename
    "备注系统ID",     # 50 - raw_data.commentsystemid
    "备注内容",       # 51 - raw_data.commenttext
    "备注时间",       # 52 - raw_data.commenttime
    "备注用户ID",     # 53 - raw_data.commentuserid
    "组件DN",         # 54 - raw_data.componentdn
    "夏令时",         # 55 - raw_data.dstsaving
    "间歇计数",       # 56 - raw_data.intermittencecount

    # 设备和位置字段组（57-86）
    "间歇重复键",     # 57 - raw_data.intermittenceduplicatedkey
    "链接",           # 58 - raw_data.link
    "链接名称",       # 59 - raw_data.linkname
    "维护状态",       # 60 - raw_data.maintainstatus
    "网元",           # 61 - raw_data.me
    "网元名称",       # 62 - raw_data.mename
    "MOC",            # 63 - raw_data.moc
    "MOC名称",        # 64 - raw_data.mocname
    "NAF过滤",        # 65 - raw_data.naffiltered
    "NBI ID",         # 66 - raw_data.nbiid
    "网元IP",         # 67 - raw_data.neip
    "网元PLMN",       # 68 - raw_data.neplmns
    "NMC原因代码",    # 69 - raw_data.nmcreasoncode
    "偏移告警时间",   # 70 - raw_data.offsetalarmraisedtime
    "操作列表",       # 71 - raw_data.operations
    "父级信息",       # 72 - raw_data.parentinfo
    "感知严重级别",   # 73 - raw_data.perceivedseverity
    "感知严重级别名", # 74 - raw_data.perceivedseverityname
    "PLMN显示信息",   # 75 - raw_data.plmndisplayinfo
    "PLMN列表",       # 76 - raw_data.plmns
    "位置",           # 77 - raw_data.position
    "位置名称",       # 78 - raw_data.positionname
    "产品资源类型",   # 79 - raw_data.productRestype
    "RAN共享开关",    # 80 - raw_data.ranshareswitch
    "原因代码",       # 81 - raw_data.reasoncode
    "原因名称",       # 82 - raw_data.reasonname
    "相关规则",       # 83 - raw_data.relatedrules
    "相关规则类型",   # 84 - raw_data.relatedruletype
    "关联标志",       # 85 - raw_data.relationflag
    "关联标志名(已移至第2列)",     # 86 - 已移动到第2列，此处保留占位

    # RAN专用字段组（87-116）
    "关联结果",       # 87 - raw_data.relationresult
    "资源路径",       # 88 - raw_data.respath
    "资源类型",       # 89 - raw_data.restype
    "资源类型名",     # 90 - raw_data.restypename
    "根源计数",       # 91 - raw_data.rootcount
    "序列号",         # 92 - raw_data.sequence
    "服务器时间",     # 93 - raw_data.servertime
    "时区ID",         # 94 - raw_data.timezoneid
    "时区偏移",       # 95 - raw_data.timezoneoffset
    "可见性",         # 96 - raw_data.visible
    "RAN EMS参数",    # 97 - raw_data.ran_ems_fm_additional_params
    "RAN告警板类型",  # 98 - raw_data.ran_fm_alarm_board_type
    "RAN告警DN",      # 99 - raw_data.ran_fm_alarm_dn
    "RAN告警位置",    # 100 - raw_data.ran_fm_alarm_location
    "RAN告警对象",    # 101 - raw_data.ran_fm_alarm_object
    "RAN告警对象ID",  # 102 - raw_data.ran_fm_alarm_object_id
    "RAN告警对象名",  # 103 - raw_data.ran_fm_alarm_object_name
    "RAN告警对象类型", # 104 - raw_data.ran_fm_alarm_object_type
    "RAN告警服务ID",  # 105 - raw_data.ran_fm_alarm_service_id
    "RAN告警站点名",  # 106 - raw_data.ran_fm_alarm_site_name
    "RAN网元虚拟化",  # 107 - raw_data.ran_fm_ne_virtualization
    "RAN SDR参数",    # 108 - raw_data.ran_sdr_fm_native_param

    # 诊断相关字段组（109-138）
    "AAX诊断结果状态", # 109 - raw_data.aax_DiagnosisResultStatus
    "AAX诊断状态",    # 110 - raw_data.aax_DiagnosisStatus
    "AAX最后诊断时间", # 111 - raw_data.aax_lastDiagnosisTime
    "AAX非关联标志",  # 112 - raw_data.aax_unrelationflag

    # 嵌套字段：S-NSSAI详细信息（113-118）
    "S-NSSAI列名",    # 113 - raw_data.S-NSSAI.columnname
    "S-NSSAI数据类型", # 114 - raw_data.S-NSSAI.datatype
    "S-NSSAI显示名",  # 115 - raw_data.S-NSSAI.displayname
    "S-NSSAI扩展字段", # 116 - raw_data.S-NSSAI.extentionfield
    "S-NSSAI值",      # 117 - raw_data.S-NSSAI.value

    # 嵌套字段：AAX诊断结果状态详细信息（118-123）
    "AAX诊断结果状态列名", # 118 - raw_data.aax_DiagnosisResultStatus.columnname
    "AAX诊断结果状态数据类型", # 119 - raw_data.aax_DiagnosisResultStatus.datatype
    "AAX诊断结果状态显示名", # 120 - raw_data.aax_DiagnosisResultStatus.displayname
    "AAX诊断结果状态扩展字段", # 121 - raw_data.aax_DiagnosisResultStatus.extentionfield
    "AAX诊断结果状态值", # 122 - raw_data.aax_DiagnosisResultStatus.value

    # 嵌套字段：AAX诊断状态详细信息（123-128）
    "AAX诊断状态列名", # 123 - raw_data.aax_DiagnosisStatus.columnname
    "AAX诊断状态数据类型", # 124 - raw_data.aax_DiagnosisStatus.datatype
    "AAX诊断状态显示名", # 125 - raw_data.aax_DiagnosisStatus.displayname
    "AAX诊断状态扩展字段", # 126 - raw_data.aax_DiagnosisStatus.extentionfield
    "AAX诊断状态值", # 127 - raw_data.aax_DiagnosisStatus.value

    # 嵌套字段：AAX最后诊断时间详细信息（128-133）
    "AAX最后诊断时间列名", # 128 - raw_data.aax_lastDiagnosisTime.columnname
    "AAX最后诊断时间数据类型", # 129 - raw_data.aax_lastDiagnosisTime.datatype
    "AAX最后诊断时间显示名", # 130 - raw_data.aax_lastDiagnosisTime.displayname
    "AAX最后诊断时间扩展字段", # 131 - raw_data.aax_lastDiagnosisTime.extentionfield
    "AAX最后诊断时间值", # 132 - raw_data.aax_lastDiagnosisTime.value

    # 嵌套字段：AAX非关联标志详细信息（133-138）
    "AAX非关联标志列名", # 133 - raw_data.aax_unrelationflag.columnname
    "AAX非关联标志数据类型", # 134 - raw_data.aax_unrelationflag.datatype
    "AAX非关联标志显示名", # 135 - raw_data.aax_unrelationflag.displayname
    "AAX非关联标志扩展字段", # 136 - raw_data.aax_unrelationflag.extentionfield
    "AAX非关联标志值", # 137 - raw_data.aax_unrelationflag.value

    # 嵌套字段：告警标题详细信息（138-143）
    "告警标题列名",   # 138 - raw_data.alarmtitle.columnname
    "告警标题数据类型", # 139 - raw_data.alarmtitle.datatype
    "告警标题显示名", # 140 - raw_data.alarmtitle.displayname
    "告警标题扩展字段", # 141 - raw_data.alarmtitle.extentionfield
    "告警标题值",     # 142 - raw_data.alarmtitle.value

    # 嵌套字段：维护状态详细信息（143-148）
    "维护状态列名",   # 143 - raw_data.maintainstatus.columnname
    "维护状态数据类型", # 144 - raw_data.maintainstatus.datatype
    "维护状态显示名", # 145 - raw_data.maintainstatus.displayname
    "维护状态扩展字段", # 146 - raw_data.maintainstatus.extentionfield
    "维护状态值",     # 147 - raw_data.maintainstatus.value

    # 嵌套字段：网元PLMN详细信息（148-150）
    "网元PLMN MCC",   # 148 - raw_data.neplmns.mcc
    "网元PLMN MNC",   # 149 - raw_data.neplmns.mnc

    # 嵌套字段：父级信息详细信息（150-155）
    "父级关联信息",   # 150 - raw_data.parentinfo.relation
    "父级关联2025-04", # 151 - raw_data.parentinfo.relation_2025_04
    "父级关联2025-06", # 152 - raw_data.parentinfo.relation_2025_06
    "父级关联2025-07", # 153 - raw_data.parentinfo.relation_2025_07
    "父级关联2025-08", # 154 - raw_data.parentinfo.relation_2025_08

    # 嵌套字段：PLMN列表详细信息（155-157）
    "PLMN MCC",       # 155 - raw_data.plmns.mcc
    "PLMN MNC",       # 156 - raw_data.plmns.mnc

    # 嵌套字段：产品资源类型详细信息（157-162）
    "产品资源类型列名", # 157 - raw_data.productRestype.columnname
    "产品资源类型数据类型", # 158 - raw_data.productRestype.datatype
    "产品资源类型显示名", # 159 - raw_data.productRestype.displayname
    "产品资源类型扩展字段", # 160 - raw_data.productRestype.extentionfield
    "产品资源类型值", # 161 - raw_data.productRestype.value

    # 嵌套字段：RAN EMS参数详细信息（162-167）
    "RAN EMS参数列名", # 162 - raw_data.ran_ems_fm_additional_params.columnname
    "RAN EMS参数数据类型", # 163 - raw_data.ran_ems_fm_additional_params.datatype
    "RAN EMS参数显示名", # 164 - raw_data.ran_ems_fm_additional_params.displayname
    "RAN EMS参数扩展字段", # 165 - raw_data.ran_ems_fm_additional_params.extentionfield
    "RAN EMS参数值", # 166 - raw_data.ran_ems_fm_additional_params.value

    # 嵌套字段：RAN告警板类型详细信息（167-172）
    "RAN告警板类型列名", # 167 - raw_data.ran_fm_alarm_board_type.columnname
    "RAN告警板类型数据类型", # 168 - raw_data.ran_fm_alarm_board_type.datatype
    "RAN告警板类型显示名", # 169 - raw_data.ran_fm_alarm_board_type.displayname
    "RAN告警板类型扩展字段", # 170 - raw_data.ran_fm_alarm_board_type.extentionfield
    "RAN告警板类型值", # 171 - raw_data.ran_fm_alarm_board_type.value

    # 嵌套字段：RAN告警DN详细信息（172-177）
    "RAN告警DN列名", # 172 - raw_data.ran_fm_alarm_dn.columnname
    "RAN告警DN数据类型", # 173 - raw_data.ran_fm_alarm_dn.datatype
    "RAN告警DN显示名", # 174 - raw_data.ran_fm_alarm_dn.displayname
    "RAN告警DN扩展字段", # 175 - raw_data.ran_fm_alarm_dn.extentionfield
    "RAN告警DN值", # 176 - raw_data.ran_fm_alarm_dn.value

    # 嵌套字段：RAN告警位置详细信息（177-182）
    "RAN告警位置列名", # 177 - raw_data.ran_fm_alarm_location.columnname
    "RAN告警位置数据类型", # 178 - raw_data.ran_fm_alarm_location.datatype
    "RAN告警位置显示名", # 179 - raw_data.ran_fm_alarm_location.displayname
    "RAN告警位置扩展字段", # 180 - raw_data.ran_fm_alarm_location.extentionfield
    "RAN告警位置值", # 181 - raw_data.ran_fm_alarm_location.value

    # 嵌套字段：RAN告警对象详细信息（182-187）
    "RAN告警对象列名", # 182 - raw_data.ran_fm_alarm_object.columnname
    "RAN告警对象数据类型", # 183 - raw_data.ran_fm_alarm_object.datatype
    "RAN告警对象显示名", # 184 - raw_data.ran_fm_alarm_object.displayname
    "RAN告警对象扩展字段", # 185 - raw_data.ran_fm_alarm_object.extentionfield
    "RAN告警对象值", # 186 - raw_data.ran_fm_alarm_object.value

    # 嵌套字段：RAN告警对象ID详细信息（187-192）
    "RAN告警对象ID列名", # 187 - raw_data.ran_fm_alarm_object_id.columnname
    "RAN告警对象ID数据类型", # 188 - raw_data.ran_fm_alarm_object_id.datatype
    "RAN告警对象ID显示名", # 189 - raw_data.ran_fm_alarm_object_id.displayname
    "RAN告警对象ID扩展字段", # 190 - raw_data.ran_fm_alarm_object_id.extentionfield
    "RAN告警对象ID值", # 191 - raw_data.ran_fm_alarm_object_id.value

    # 嵌套字段：RAN告警对象名详细信息（192-197）
    "RAN告警对象名列名", # 192 - raw_data.ran_fm_alarm_object_name.columnname
    "RAN告警对象名数据类型", # 193 - raw_data.ran_fm_alarm_object_name.datatype
    "RAN告警对象名显示名", # 194 - raw_data.ran_fm_alarm_object_name.displayname
    "RAN告警对象名扩展字段", # 195 - raw_data.ran_fm_alarm_object_name.extentionfield
    "RAN告警对象名值", # 196 - raw_data.ran_fm_alarm_object_name.value

    # 嵌套字段：RAN告警对象类型详细信息（197-202）
    "RAN告警对象类型列名", # 197 - raw_data.ran_fm_alarm_object_type.columnname
    "RAN告警对象类型数据类型", # 198 - raw_data.ran_fm_alarm_object_type.datatype
    "RAN告警对象类型显示名", # 199 - raw_data.ran_fm_alarm_object_type.displayname
    "RAN告警对象类型扩展字段", # 200 - raw_data.ran_fm_alarm_object_type.extentionfield
    "RAN告警对象类型值", # 201 - raw_data.ran_fm_alarm_object_type.value

    # 最后的字段（202-203）
    "相关规则类型空值", # 202 - raw_data.relatedruletype.null
    "关联结果值",     # 203 - raw_data.relationresult.value
]



# 文本截断长度
MAX_CODE_NAME_LENGTH = 80
MAX_ME_NAME_LENGTH = 200  # 增加网元名称显示长度到200字符
MAX_IP_LENGTH = 30

# 颜色配置 (RGB)
COLOR_ROOT_ALARM = (255, 230, 230)      # 根源告警 - 红色背景
COLOR_DERIVED_ALARM = (255, 248, 225)   # 衍生告警 - 橙色背景
COLOR_FOCUS_ALARM = (231, 243, 255)     # 重点关注 - 蓝色背景
COLOR_NEW_ALARM = (255, 243, 205)       # 新告警 - 黄色背景
COLOR_NORMAL = (255, 255, 255)          # 普通告警 - 白色背景

# 邮件预设配置
EMAIL_PRESETS = {
    '<EMAIL>': {
        'name': '个人QQ邮箱',
        'smtp_host': 'smtp.qq.com',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'ftyyonprjcetbcei',  # 需要填入授权码
        'from_addr': '<EMAIL>',
        'to_addr': '<EMAIL>',
    },
    '<EMAIL>': {
        'name': '工作Foxmail',
        'smtp_host': 'smtp.qq.com',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'ftyyonprjcetbcei',  # 已有授权码
        'from_addr': '<EMAIL>',
        'to_addr': '<EMAIL>',
    },
    '<EMAIL>': {
        'name': '联通企业邮箱',
        'smtp_host': 'xcs.mail.chinaunicom.cn',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '<EMAIL>',
        'password': 'OAbbd520.',
        'from_addr': '<EMAIL>',
        'to_addr': '<EMAIL>',
    },
    'custom': {
        'name': '自定义配置',
        'smtp_host': '',
        'smtp_port': 465,
        'use_ssl': True,
        'username': '',
        'password': '',
        'from_addr': '',
        'to_addr': '',
    }
}

# 邮件默认配置（向后兼容）
EMAIL_DEFAULTS = EMAIL_PRESETS['<EMAIL>'].copy()
EMAIL_DEFAULTS['enabled'] = False

# 邮件加密配置
EMAIL_ENCRYPTION_DEFAULTS = {
    'encrypt_enabled': False,  # 默认不加密，保持向后兼容
    'encryption_password': '',  # 加密密码
    'include_decrypt_info': True,  # 是否在邮件中包含解密说明
}

class EmailEncryption:
    """邮件加密工具类 - Linus式简单实用"""

    @staticmethod
    def generate_key_from_password(password: str, salt: bytes = None) -> bytes:
        """从密码生成加密密钥"""
        if not CRYPTO_AVAILABLE:
            raise Exception("加密库未安装，请运行: pip install cryptography")

        if salt is None:
            salt = b'alarm_monitor_salt_2024'  # 固定盐值，确保相同密码生成相同密钥

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password.encode()))

    @staticmethod
    def encrypt_content(content: str, password: str) -> str:
        """加密内容 - Unicode乱码版本"""
        if not CRYPTO_AVAILABLE:
            return content  # 降级到明文

        try:
            key = EmailEncryption.generate_key_from_password(password)
            f = Fernet(key)
            encrypted_bytes = f.encrypt(content.encode('utf-8'))

            # 转换为Unicode乱码符号
            return EmailEncryption._bytes_to_unicode_chaos(encrypted_bytes)
        except Exception as e:
            print(f"加密失败，使用明文: {e}")
            return content

    @staticmethod
    def _bytes_to_unicode_chaos(data: bytes) -> str:
        """将字节数据转换为Unicode乱码符号 - 简化可靠版本"""
        # 先转换为Base64，然后将Base64字符映射为Unicode符号
        import base64
        b64_data = base64.b64encode(data).decode('ascii')

        # Base64字符到Unicode符号的映射表
        char_to_symbol = {
            'A': '☀', 'B': '☁', 'C': '☂', 'D': '☃', 'E': '☄', 'F': '★', 'G': '☆', 'H': '☇',
            'I': '☈', 'J': '☉', 'K': '☊', 'L': '☋', 'M': '☌', 'N': '☍', 'O': '☎', 'P': '☏',
            'Q': '☐', 'R': '☑', 'S': '☒', 'T': '☓', 'U': '☔', 'V': '☕', 'W': '☖', 'X': '☗',
            'Y': '☘', 'Z': '☙', 'a': '☚', 'b': '☛', 'c': '☜', 'd': '☝', 'e': '☞', 'f': '☟',
            'g': '☠', 'h': '☡', 'i': '☢', 'j': '☣', 'k': '☤', 'l': '☥', 'm': '☦', 'n': '☧',
            'o': '☨', 'p': '☩', 'q': '☪', 'r': '☫', 's': '☬', 't': '☭', 'u': '☮', 'v': '☯',
            'w': '☰', 'x': '☱', 'y': '☲', 'z': '☳', '0': '☴', '1': '☵', '2': '☶', '3': '☷',
            '4': '☸', '5': '☹', '6': '☺', '7': '☻', '8': '☼', '9': '☽', '+': '☾', '/': '☿',
            '=': '♀'
        }

        # 将Base64字符转换为Unicode符号
        result = ""
        for char in b64_data:
            result += char_to_symbol.get(char, '♁')  # 使用♁作为未知字符的备用符号

        return result

    @staticmethod
    def _unicode_chaos_to_bytes(chaos_text: str) -> bytes:
        """将Unicode乱码符号转换回字节数据 - 简化可靠版本"""
        # Unicode符号到Base64字符的反向映射表
        symbol_to_char = {
            '☀': 'A', '☁': 'B', '☂': 'C', '☃': 'D', '☄': 'E', '★': 'F', '☆': 'G', '☇': 'H',
            '☈': 'I', '☉': 'J', '☊': 'K', '☋': 'L', '☌': 'M', '☍': 'N', '☎': 'O', '☏': 'P',
            '☐': 'Q', '☑': 'R', '☒': 'S', '☓': 'T', '☔': 'U', '☕': 'V', '☖': 'W', '☗': 'X',
            '☘': 'Y', '☙': 'Z', '☚': 'a', '☛': 'b', '☜': 'c', '☝': 'd', '☞': 'e', '☟': 'f',
            '☠': 'g', '☡': 'h', '☢': 'i', '☣': 'j', '☤': 'k', '☥': 'l', '☦': 'm', '☧': 'n',
            '☨': 'o', '☩': 'p', '☪': 'q', '☫': 'r', '☬': 's', '☭': 't', '☮': 'u', '☯': 'v',
            '☰': 'w', '☱': 'x', '☲': 'y', '☳': 'z', '☴': '0', '☵': '1', '☶': '2', '☷': '3',
            '☸': '4', '☹': '5', '☺': '6', '☻': '7', '☼': '8', '☽': '9', '☾': '+', '☿': '/',
            '♀': '='
        }

        # 将Unicode符号转换回Base64字符
        b64_data = ""
        for symbol in chaos_text:
            b64_data += symbol_to_char.get(symbol, 'A')  # 使用'A'作为未知符号的备用字符

        # 解码Base64
        import base64
        try:
            return base64.b64decode(b64_data.encode('ascii'))
        except Exception:
            # 如果Base64解码失败，返回空字节
            return b''

    @staticmethod
    def decrypt_content(encrypted_content: str, password: str) -> str:
        """解密内容 - Unicode乱码版本"""
        if not CRYPTO_AVAILABLE:
            return encrypted_content

        try:
            key = EmailEncryption.generate_key_from_password(password)
            f = Fernet(key)

            # 将Unicode乱码符号转换回字节数据
            encrypted_bytes = EmailEncryption._unicode_chaos_to_bytes(encrypted_content)
            decrypted_bytes = f.decrypt(encrypted_bytes)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            print(f"解密失败: {e}")
            return f"[解密失败] {encrypted_content[:100]}..."

    @staticmethod
    def create_decrypt_instructions(password: str) -> str:
        """生成解密说明 - Unicode乱码版本"""
        return f"""
=== 邮件内容已加密（Unicode乱码格式）===
解密方法：
1. 复制加密内容（-----BEGIN ENCRYPTED CONTENT----- 到 -----END ENCRYPTED CONTENT----- 之间的乱码符号）
2. 使用Python解密：

   from cryptography.fernet import Fernet
   from cryptography.hazmat.primitives import hashes
   from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
   import base64

   def decrypt_unicode_chaos_email(chaos_content, password):
       # 将Unicode乱码转换回字节
       def unicode_chaos_to_bytes(chaos_text):
           symbol_ranges = [
               (0x2600, 0x26FF), (0x2700, 0x27BF), (0x1F300, 0x1F5FF),
               (0x25A0, 0x25FF), (0x2190, 0x21FF)
           ]
           result = bytearray()
           for i, char in enumerate(chaos_text):
               char_code = ord(char)
               range_idx = i % len(symbol_ranges)
               start, end = symbol_ranges[range_idx]
               if start <= char_code < end:
                   byte_val = char_code - start
               else:
                   byte_val = char_code - 0x2600
                   if byte_val < 0 or byte_val > 255:
                       byte_val = char_code % 256
               result.append(byte_val % 256)
           return bytes(result)

       # 解密
       salt = b'alarm_monitor_salt_2024'
       kdf = PBKDF2HMAC(algorithm=hashes.SHA256(), length=32, salt=salt, iterations=100000)
       key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
       f = Fernet(key)
       encrypted_bytes = unicode_chaos_to_bytes(chaos_content)
       return f.decrypt(encrypted_bytes).decode('utf-8')

   # 使用方法
   decrypted = decrypt_unicode_chaos_email("乱码内容", "{password}")
   print(decrypted)

3. 密码: {password}
4. 注意：加密内容显示为各种Unicode符号（☀☁☂★✈🌀■←等），这是正常的加密效果
========================
"""

class EmailManager:
    """统一的邮件管理器 - 解决配置分散和状态不一致问题"""

    def __init__(self, config_file, parent=None):
        self.config_file = config_file
        self.parent = parent
        self.enabled = False
        self.settings = {}
        self.encryption_settings = {}
        self.last_check_time = 0
        self.check_interval = 60  # 60秒检查一次配置文件变化

        # 启动时加载配置
        self.load_config()

    def load_config(self):
        """从配置文件加载邮件设置"""
        import configparser, os, time
        cfg = configparser.ConfigParser()
        if os.path.exists(self.config_file):
            try:
                cfg.read(self.config_file, encoding='utf-8')
            except Exception:
                pass

        # 加载邮件配置
        section = 'email'
        self.enabled = cfg.getboolean(section, 'enabled', fallback=EMAIL_DEFAULTS['enabled'])
        self.settings = EMAIL_DEFAULTS.copy()

        if cfg.has_section(section):
            self.settings['enabled'] = self.enabled
            self.settings['smtp_host'] = cfg.get(section, 'smtp_host', fallback=self.settings['smtp_host'])
            self.settings['smtp_port'] = cfg.getint(section, 'smtp_port', fallback=self.settings['smtp_port'])
            self.settings['use_ssl'] = cfg.getboolean(section, 'use_ssl', fallback=self.settings['use_ssl'])
            self.settings['username'] = cfg.get(section, 'username', fallback=self.settings['username'])
            self.settings['password'] = cfg.get(section, 'password', fallback=self.settings['password'])
            self.settings['from_addr'] = cfg.get(section, 'from_addr', fallback=self.settings['from_addr'])
            self.settings['to_addr'] = cfg.get(section, 'to_addr', fallback=self.settings['to_addr'])
            self.settings['enable_image_attachment'] = cfg.getboolean(section, 'enable_image_attachment', fallback=False)

        # 加载加密配置
        encrypt_section = 'email_encryption'
        self.encryption_settings = EMAIL_ENCRYPTION_DEFAULTS.copy()

        if cfg.has_section(encrypt_section):
            self.encryption_settings['encrypt_enabled'] = cfg.getboolean(encrypt_section, 'encrypt_enabled', fallback=False)
            self.encryption_settings['encryption_password'] = cfg.get(encrypt_section, 'encryption_password', fallback='')
            self.encryption_settings['include_decrypt_info'] = cfg.getboolean(encrypt_section, 'include_decrypt_info', fallback=True)

        self.last_check_time = time.time()

        # 延迟日志记录，避免在UI初始化前调用add_log
        if self.parent and hasattr(self.parent, 'log_text'):
            status = "启用" if self.enabled else "禁用"
            encrypt_status = "加密" if self.encryption_settings.get('encrypt_enabled') else "明文"
            image_status = "启用" if self.settings.get('enable_image_attachment', False) else "关闭"
            self.parent.add_log(f"📧 邮件功能: {status} | 内容: {encrypt_status} | 图片: {image_status}")

    def is_enabled(self):
        """检查邮件是否启用（带缓存机制，避免频繁读文件）"""
        import time
        current_time = time.time()

        # 定期检查配置文件是否有变化
        if current_time - self.last_check_time > self.check_interval:
            old_enabled = self.enabled
            self.load_config()

            # 如果状态发生变化，记录日志
            if old_enabled != self.enabled and self.parent and hasattr(self.parent, 'add_log'):
                status = "启用" if self.enabled else "禁用"
                self.parent.add_log(f"📧 邮件功能已{status}")

        return self.enabled

    def reload_config(self):
        """立即重新加载配置（用户修改配置时调用）"""
        old_enabled = self.enabled
        self.load_config()

        # 记录配置更改
        if self.parent and hasattr(self.parent, 'add_log'):
            if old_enabled != self.enabled:
                status = "启用" if self.enabled else "禁用"
                self.parent.add_log(f"📧 邮件功能已{status}")
            else:
                self.parent.add_log("📧 邮件配置已更新")

    def get_settings(self):
        """获取当前邮件设置"""
        return self.settings.copy()

    def get_encryption_settings(self):
        """获取当前加密设置"""
        return self.encryption_settings.copy()

    def is_encryption_enabled(self):
        """检查是否启用加密"""
        return self.encryption_settings.get('encrypt_enabled', False) and self.encryption_settings.get('encryption_password', '').strip()

    def encrypt_email_content(self, content):
        """加密邮件内容（如果启用）"""
        if not self.is_encryption_enabled():
            return content, False  # 返回原内容和是否加密的标志

        password = self.encryption_settings.get('encryption_password', '')
        if not password.strip():
            return content, False

        try:
            encrypted = EmailEncryption.encrypt_content(content, password)
            return encrypted, True
        except Exception as e:
            if self.parent and hasattr(self.parent, 'add_log'):
                self.parent.add_log(f"📧 加密失败，使用明文: {e}")
            return content, False

    def validate_settings(self):
        """验证邮件设置是否完整"""
        if not self.enabled:
            return False, "邮件功能未启用"

        required_fields = ['smtp_host', 'username', 'from_addr', 'to_addr']
        for field in required_fields:
            if not self.settings.get(field, '').strip():
                return False, f"缺少必要配置: {field}"

        # 检查邮箱格式
        from_addr = self.settings['from_addr'].strip()
        to_addr = self.settings['to_addr'].strip()
        if '@' not in from_addr or '@' not in to_addr:
            return False, f"邮箱格式错误: 发件人={from_addr}, 收件人={to_addr}"

        # 检查加密配置
        if self.encryption_settings.get('encrypt_enabled', False):
            password = self.encryption_settings.get('encryption_password', '').strip()
            if not password:
                return False, "启用加密但未设置加密密码"
            if not CRYPTO_AVAILABLE:
                return False, "启用加密但未安装加密库，请运行: pip install cryptography"

        return True, "配置正确"

# 持续阈值（分钟）
SUSTAINED_THRESHOLDS_MINUTES = [60, 120, 180, 240, 480, 1440, 2880, 4320]


# 配置文件
CONFIG_FILE = "monitor_config.ini"

# 筛选条件已删除

# Web.py命令参数
WEB_FETCH_COMMAND = [
    "--fetch",
    "--headless"
]

# 网管系统登录配置
DEFAULT_LOGIN_URL = "https://*************:28001/uportal/framework/default.html"
DEFAULT_ALARM_URL = "https://*************:28001/oss/framework/components/platform/modules/alarm/alarm.html"

# 检查是否有playwright
try:
    from playwright.async_api import async_playwright
    import asyncio
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# 网管抓取相关常量 - 直接从web.py复制
DEFAULT_BASE_URL = "https://*************:28001"
DEFAULT_LOGIN_URL = "https://*************:28001/uportal/framework/default.html"
DEFAULT_ALARM_URL = "https://*************:28001/uportal/framework/default.html#/_ngict-fm-currentAlarm"

# 登录选择器 - 直接从web.py复制
LOGIN_SELECTORS = {
    "username_input": "#inputUserName",
    "password_input": "#inputCiphercode",
    "login_button": "#loginBut",
}

# 备用菜单选择器 - 直接从web.py复制
MENU_SELECTORS = {
    "alarm_menu": 'text="告警管理"',
    "current_alarm": 'text="当前告警"',
}

# 能触发表格接口请求的按钮/元素 - 直接从web.py复制
REFRESH_TRIGGERS = [
    'button:has-text("刷新")',
    'button:has-text("查询")',
    'button:has-text("搜索")',
    'button[title*="刷新"]',
    'button[title*="查询"]',
    '.refresh-btn',
    '.query-btn',
    '.search-btn'
]

# 判定是"当前告警表格接口"的 URL 片段 - 直接从web.py复制
API_URL_SUBSTRINGS = [
    "/api/fm-active/v1/activealarms/table",
    "/activealarms/table",
]

# 抓取配置
FETCH_CONFIG = {
    "page_size": 500,
    "max_pages": 50,
    "timeout": 30000,
    "retry_count": 3
}

def calculate_effective_duration(start_time, end_time):
    """
    计算考核持续时间（排除每天0:00-5:59的非考核时间段）

    Args:
        start_time: datetime对象
        end_time: datetime对象

    Returns:
        int: 考核持续时间（分钟）
    """
    from datetime import timedelta

    if start_time >= end_time:
        return 0

    total_effective_minutes = 0
    current_time = start_time

    while current_time < end_time:
        # 获取当天的考核时间段开始和结束
        day_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
        assessment_start = day_start.replace(hour=6)  # 6:00 考核开始
        assessment_end = day_start.replace(hour=23, minute=59, second=59)  # 23:59 考核结束
        next_day_start = day_start + timedelta(days=1)

        # 计算当天的有效时间段
        day_effective_start = max(current_time, assessment_start)
        day_effective_end = min(end_time, assessment_end)

        # 如果当天有考核时间段的重叠
        if day_effective_start <= day_effective_end and day_effective_start.hour >= 6:
            duration = day_effective_end - day_effective_start
            total_effective_minutes += int(duration.total_seconds() / 60)

        # 移动到下一天
        current_time = next_day_start

    return total_effective_minutes

from PySide6.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox

class EmailConfigDialog(QDialog):
    def __init__(self, parent=None, config_file=CONFIG_FILE):
        super().__init__(parent)
        self.setWindowTitle("📧 邮件设置")
        self.resize(480, 360)
        self.config_file = config_file
        self.setup_ui()
        self.load_email_config()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 添加邮箱预设选择
        from PySide6.QtWidgets import QLineEdit, QCheckBox, QLabel, QComboBox, QHBoxLayout, QPushButton

        preset_layout = QHBoxLayout()
        preset_label = QLabel("📮 快速选择邮箱:")
        preset_label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.preset_combo = QComboBox()
        self.preset_combo.setMinimumWidth(200)
        self.preset_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #3498db;
                border-radius: 5px;
                background-color: white;
            }
            QComboBox:hover {
                border-color: #2980b9;
            }
        """)

        # 添加预设选项
        for key, preset in EMAIL_PRESETS.items():
            self.preset_combo.addItem(preset['name'], key)

        # 连接选择事件
        self.preset_combo.currentTextChanged.connect(self.on_preset_changed)

        preset_layout.addWidget(preset_label)
        preset_layout.addWidget(self.preset_combo)
        preset_layout.addStretch()
        layout.addLayout(preset_layout)

        # 添加分隔线
        separator = QLabel()
        separator.setFixedHeight(1)
        separator.setStyleSheet("background-color: #bdc3c7; margin: 10px 0;")
        layout.addWidget(separator)

        form = QFormLayout()

        self.enabled_cb = QCheckBox("启用发送告警邮件")
        self.enabled_cb.setStyleSheet("font-weight: bold; color: #27ae60;")

        self.smtp_host = QLineEdit()
        self.smtp_host.setPlaceholderText("例如：smtp.qq.com")

        self.smtp_port = QLineEdit()
        self.smtp_port.setPlaceholderText("例如：465")

        self.use_ssl_cb = QCheckBox("使用SSL")

        self.username = QLineEdit()
        self.username.setPlaceholderText("例如：<EMAIL>")

        self.password = QLineEdit()
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setPlaceholderText("请输入邮箱密码或授权码")

        self.from_addr = QLineEdit()
        self.from_addr.setPlaceholderText("例如：<EMAIL>")

        self.to_addr = QLineEdit()
        self.to_addr.setPlaceholderText("例如：<EMAIL>")

        form.addRow("SMTP服务器:", self.smtp_host)
        form.addRow("端口:", self.smtp_port)
        form.addRow("SSL:", self.use_ssl_cb)
        form.addRow("用户名:", self.username)
        form.addRow("密码:", self.password)
        form.addRow("发件人:", self.from_addr)
        form.addRow("收件人:", self.to_addr)

        layout.addWidget(self.enabled_cb)
        layout.addLayout(form)

        # 添加图片附件选项
        self.enable_image_attachment = QCheckBox("生成图片附件")
        self.enable_image_attachment.setToolTip("将邮件正文内容生成为图片附件（白底黑字）")
        layout.addWidget(self.enable_image_attachment)

        # 添加发送模式说明
        mode_info = QLabel("""
📨 邮件发送模式：拆分发送
• 每个告警组发送一封独立邮件，便于跟踪处理
• 发送间隔0.1秒，快速发送
• 重要告警不会被淹没在长邮件中
        """.strip())
        mode_info.setWordWrap(True)
        mode_info.setStyleSheet("color: #27ae60; font-size: 11px; padding: 10px; background-color: #f0f8f0; border-radius: 5px; font-weight: bold;")
        layout.addWidget(mode_info)

        # 添加加密设置分组
        encrypt_group = QGroupBox("🔐 邮件内容加密设置")
        encrypt_layout = QVBoxLayout(encrypt_group)

        # 加密开关
        self.encrypt_enabled_cb = QCheckBox("启用邮件内容加密")
        self.encrypt_enabled_cb.setStyleSheet("font-weight: bold; color: #e74c3c;")
        encrypt_layout.addWidget(self.encrypt_enabled_cb)

        # 加密密码
        encrypt_form = QFormLayout()
        self.encrypt_password = QLineEdit()
        self.encrypt_password.setEchoMode(QLineEdit.Password)
        self.encrypt_password.setPlaceholderText("设置加密密码（接收方需要此密码解密）")

        self.include_decrypt_info_cb = QCheckBox("在邮件中包含解密说明")
        self.include_decrypt_info_cb.setChecked(True)

        encrypt_form.addRow("加密密码:", self.encrypt_password)
        encrypt_form.addRow("解密说明:", self.include_decrypt_info_cb)
        encrypt_layout.addLayout(encrypt_form)

        # 加密说明
        encrypt_info = QLabel("""
🔐 Unicode乱码加密说明：
• 启用后，邮件正文将显示为Unicode乱码符号（☀☁☂★✈🌀■←等）
• 加密内容看起来像各种特殊符号，完全无法直接阅读
• 接收方需要密码和专用解密程序才能查看告警内容
• 建议密码长度至少8位，包含字母数字
• 密码请通过安全渠道告知接收方
• 如果忘记密码，加密内容将无法恢复
• 乱码效果比Base64更隐蔽，安全性更高
        """.strip())
        encrypt_info.setWordWrap(True)
        encrypt_info.setStyleSheet("color: #666; font-size: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        encrypt_layout.addWidget(encrypt_info)

        layout.addWidget(encrypt_group)

        # 设置按钮
        self.setup_buttons(layout)

    def on_preset_changed(self):
        """当预设选择改变时，自动填充配置"""
        current_data = self.preset_combo.currentData()
        if current_data and current_data in EMAIL_PRESETS:
            preset = EMAIL_PRESETS[current_data]

            # 填充配置字段
            self.smtp_host.setText(preset['smtp_host'])
            self.smtp_port.setText(str(preset['smtp_port']))
            self.use_ssl_cb.setChecked(preset['use_ssl'])
            self.username.setText(preset['username'])

            # 只有在密码不为空时才填充（避免覆盖用户输入的密码）
            if preset['password']:
                self.password.setText(preset['password'])

            self.from_addr.setText(preset['from_addr'])
            self.to_addr.setText(preset['to_addr'])

            # 更新占位符提示
            if 'qq.com' in preset['smtp_host']:
                self.password.setPlaceholderText("请输入QQ邮箱授权码（不是QQ密码）")
            elif 'chinaunicom.cn' in preset['smtp_host']:
                self.password.setPlaceholderText("请输入联通邮箱登录密码")
            else:
                self.password.setPlaceholderText("请输入邮箱密码")

    def setup_buttons(self, layout):
        """设置按钮"""
        # 添加测试按钮
        from PySide6.QtWidgets import QPushButton
        test_btn = QPushButton("🧪 发送测试邮件")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        test_btn.clicked.connect(self.test_email_connection)
        layout.addWidget(test_btn)

        btns = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.save_email_config)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)

    def load_email_config(self):
        import configparser, os
        cfg = configparser.ConfigParser()
        if os.path.exists(self.config_file):
            cfg.read(self.config_file, encoding='utf-8')
        section = 'email'

        # 应用配置
        self.enabled_cb.setChecked(cfg.getboolean(section, 'enabled', fallback=EMAIL_DEFAULTS['enabled']))
        username = cfg.get(section, 'username', fallback=EMAIL_DEFAULTS['username'])

        # 根据用户名自动选择对应的预设
        preset_key = 'custom'  # 默认为自定义
        for key, preset in EMAIL_PRESETS.items():
            if preset['username'] == username:
                preset_key = key
                break

        # 设置预设选择
        for i in range(self.preset_combo.count()):
            if self.preset_combo.itemData(i) == preset_key:
                self.preset_combo.setCurrentIndex(i)
                break

        # 填充配置字段
        self.smtp_host.setText(cfg.get(section, 'smtp_host', fallback=EMAIL_DEFAULTS['smtp_host']))
        self.smtp_port.setText(str(cfg.getint(section, 'smtp_port', fallback=EMAIL_DEFAULTS['smtp_port'])))
        self.use_ssl_cb.setChecked(cfg.getboolean(section, 'use_ssl', fallback=EMAIL_DEFAULTS['use_ssl']))
        self.username.setText(username)
        self.password.setText(cfg.get(section, 'password', fallback=EMAIL_DEFAULTS['password']))
        self.from_addr.setText(cfg.get(section, 'from_addr', fallback=EMAIL_DEFAULTS['from_addr']))
        self.to_addr.setText(cfg.get(section, 'to_addr', fallback=EMAIL_DEFAULTS['to_addr']))

        # 加载图片附件选项
        self.enable_image_attachment.setChecked(cfg.getboolean(section, 'enable_image_attachment', fallback=False))

        # 加载加密配置
        encrypt_section = 'email_encryption'
        self.encrypt_enabled_cb.setChecked(cfg.getboolean(encrypt_section, 'encrypt_enabled', fallback=EMAIL_ENCRYPTION_DEFAULTS['encrypt_enabled']))
        self.encrypt_password.setText(cfg.get(encrypt_section, 'encryption_password', fallback=EMAIL_ENCRYPTION_DEFAULTS['encryption_password']))
        self.include_decrypt_info_cb.setChecked(cfg.getboolean(encrypt_section, 'include_decrypt_info', fallback=EMAIL_ENCRYPTION_DEFAULTS['include_decrypt_info']))

    def save_email_config(self):
        import configparser
        cfg = configparser.ConfigParser()
        try:
            cfg.read(self.config_file, encoding='utf-8')
        except Exception:
            pass

        # 保存邮件配置
        if not cfg.has_section('email'):
            cfg.add_section('email')
        cfg.set('email', 'enabled', 'true' if self.enabled_cb.isChecked() else 'false')
        cfg.set('email', 'smtp_host', self.smtp_host.text().strip())
        cfg.set('email', 'smtp_port', self.smtp_port.text().strip())
        cfg.set('email', 'use_ssl', 'true' if self.use_ssl_cb.isChecked() else 'false')
        cfg.set('email', 'username', self.username.text().strip())
        cfg.set('email', 'password', self.password.text())
        cfg.set('email', 'from_addr', self.from_addr.text().strip())
        cfg.set('email', 'to_addr', self.to_addr.text().strip())
        cfg.set('email', 'enable_image_attachment', 'true' if self.enable_image_attachment.isChecked() else 'false')

        # 保存加密配置
        if not cfg.has_section('email_encryption'):
            cfg.add_section('email_encryption')
        cfg.set('email_encryption', 'encrypt_enabled', 'true' if self.encrypt_enabled_cb.isChecked() else 'false')
        cfg.set('email_encryption', 'encryption_password', self.encrypt_password.text())
        cfg.set('email_encryption', 'include_decrypt_info', 'true' if self.include_decrypt_info_cb.isChecked() else 'false')

        with open(self.config_file, 'w', encoding='utf-8') as f:
            cfg.write(f)

        # 通知主程序的邮件管理器重新加载配置
        if hasattr(self.parent(), 'email_manager'):
            self.parent().email_manager.reload_config()

        self.accept()

    def test_email_connection(self):
        """测试邮件连接和发送"""
        from PySide6.QtWidgets import QMessageBox
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        import smtplib
        from datetime import datetime

        # 获取当前配置
        settings = {
            'smtp_host': self.smtp_host.text().strip(),
            'smtp_port': int(self.smtp_port.text().strip()) if self.smtp_port.text().strip().isdigit() else 465,
            'use_ssl': self.use_ssl_cb.isChecked(),
            'username': self.username.text().strip(),
            'password': self.password.text(),
            'from_addr': self.from_addr.text().strip(),
            'to_addr': self.to_addr.text().strip()
        }

        # 验证必填字段
        if not all([settings['smtp_host'], settings['username'], settings['password'],
                   settings['from_addr'], settings['to_addr']]):
            QMessageBox.warning(self, "错误", "请填写完整的邮件配置信息")
            return

        try:
            # 创建测试邮件
            msg = MIMEMultipart('alternative')

            # 设置完整的邮件头部信息
            import uuid
            from email.utils import formatdate, make_msgid

            msg['Subject'] = f"[测试邮件] 告警监控系统邮件测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            msg['From'] = settings['from_addr']
            msg['To'] = settings['to_addr']
            msg['Date'] = formatdate(localtime=True)
            msg['Message-ID'] = make_msgid(domain='alarm-monitor.local')
            msg['X-Mailer'] = 'AlarmMonitor/2.0'
            msg['X-Priority'] = '3'  # 普通优先级
            msg['Importance'] = 'Normal'
            msg['Auto-Submitted'] = 'auto-generated'  # 标识为自动生成邮件
            msg['X-Auto-Response-Suppress'] = 'All'  # 抑制自动回复

            # 添加UUID随机注释降低内容相似度
            random_uuid = str(uuid.uuid4())

            test_content = f"""
告警监控系统邮件测试

测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
发件人: {settings['from_addr']}
收件人: {settings['to_addr']}
SMTP服务器: {settings['smtp_host']}:{settings['smtp_port']}
SSL: {'是' if settings['use_ssl'] else '否'}

如果您收到这封邮件，说明邮件配置正确！

请检查：
1. 收件箱
2. 垃圾邮件文件夹
3. 已删除文件夹
4. 邮件过滤规则

告警监控系统

<!-- UUID: {random_uuid} -->
"""
            msg.attach(MIMEText(test_content, 'plain', 'utf-8'))

            # 发送邮件
            server = None
            if settings['use_ssl']:
                server = smtplib.SMTP_SSL(settings['smtp_host'], settings['smtp_port'], timeout=15)
            else:
                server = smtplib.SMTP(settings['smtp_host'], settings['smtp_port'], timeout=15)

            server.login(settings['username'], settings['password'])
            result = server.sendmail(settings['from_addr'], [settings['to_addr']], msg.as_string())
            server.quit()

            if result:
                QMessageBox.warning(self, "警告", f"邮件发送完成但有警告:\n{result}")
            else:
                QMessageBox.information(self, "成功",
                    f"测试邮件发送成功！\n\n"
                    f"请检查收件箱: {settings['to_addr']}\n"
                    f"如果没有收到，请检查垃圾邮件文件夹")

        except smtplib.SMTPAuthenticationError as e:
            QMessageBox.critical(self, "认证失败", f"SMTP认证失败:\n{e}")
        except smtplib.SMTPRecipientsRefused as e:
            QMessageBox.critical(self, "收件人错误", f"收件人被拒绝:\n{e}")
        except smtplib.SMTPSenderRefused as e:
            QMessageBox.critical(self, "发件人错误", f"发件人被拒绝:\n{e}")
        except Exception as e:
            QMessageBox.critical(self, "发送失败", f"邮件发送失败:\n{e}")

def format_duration_text(minutes):
    """格式化持续时间显示"""
    if minutes <= 0:
        return "0分钟"

    days = minutes // (24 * 60)
    hours = (minutes % (24 * 60)) // 60
    mins = minutes % 60

    parts = []
    if days > 0:
        parts.append(f"{days}天")
    if hours > 0:
        parts.append(f"{hours}小时")
    if mins > 0:
        parts.append(f"{mins}分钟")

    return "".join(parts) if parts else "0分钟"

def create_database_structure(db_path):
    """创建数据库表结构 - 直接从web.py复制"""
    import time
    retries = 3
    for attempt in range(retries):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 创建告警表 - 直接从web.py复制
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS alarms (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alarm_key TEXT UNIQUE,

                    -- 基础告警信息
                    code_name TEXT,
                    perceived_severity_name TEXT,
                    ack_state_name TEXT,
                    me_name TEXT,
                    alarm_raised_time INTEGER,
                    alarm_type_name TEXT,
                    reason_name TEXT,
                    ne_ip TEXT,
                    additional_text TEXT,
                    position_name TEXT,
                    alarm_code TEXT,
                    res_type_name TEXT,
                    clear_state_name TEXT,
                    ack_user_id TEXT,
                    comment_text TEXT,

                    -- 原始数据
                    raw_data TEXT,

                    -- 时间戳字段
                    first_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_new INTEGER DEFAULT 1,
                    is_active INTEGER DEFAULT 1,
                    status_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 添加持续时间字段
            try:
                cursor.execute("ALTER TABLE alarms ADD COLUMN actual_duration_minutes INTEGER DEFAULT 0")
                print("📄 添加持续时间字段...")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute("ALTER TABLE alarms ADD COLUMN effective_duration_minutes INTEGER DEFAULT 0")
                print("📄 持续时间字段添加完成")
            except sqlite3.OperationalError:
                pass

            # 添加基线标识字段
            try:
                cursor.execute("ALTER TABLE alarms ADD COLUMN is_baseline INTEGER DEFAULT 0")
                print("📄 添加基线标识字段...")
            except sqlite3.OperationalError:
                pass

            print("📄 基线标识字段添加完成")

            # 创建索引 - 优化查询性能（不改变任何业务逻辑）
            try:
                # 原有索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_alarm_key ON alarms(alarm_key)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_alarm_raised_time ON alarms(alarm_raised_time)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_active_new ON alarms(is_active, is_new)")

                # 新增性能优化索引（100%安全，只加速查询）
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_is_active ON alarms(is_active)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_is_active_baseline ON alarms(is_active, is_baseline)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_code_name ON alarms(code_name)")

                print("📊 数据库索引创建完成，查询性能已优化")
            except sqlite3.OperationalError as e:
                print(f"创建索引时出错（可能是字段不存在）: {e}")

            conn.commit()
            conn.close()

            print(f"📄 数据库表结构创建完成: {db_path}")
            break

        except sqlite3.OperationalError as e:
            if "database is locked" in str(e) and attempt < retries - 1:
                print(f"数据库锁定，等待重试... (尝试 {attempt + 1}/{retries})")
                time.sleep(2 ** attempt)
                continue
            else:
                raise e

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
    QWidget, QTableWidget, QTableWidgetItem, QLabel,
    QPushButton, QGroupBox,
    QHeaderView, QDialog, QTextEdit,
    QSplitter, QProgressBar, QStatusBar,
    QPlainTextEdit, QCheckBox
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QColor, QTextCursor




class ConfigDialog(QDialog):
    """配置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("网管系统配置")
        self.setModal(True)
        self.resize(400, 200)

        layout = QVBoxLayout(self)

        # 表单
        from PySide6.QtWidgets import QFormLayout, QLineEdit, QDialogButtonBox
        form_layout = QFormLayout()

        self.username_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)

        form_layout.addRow("用户名:", self.username_edit)
        form_layout.addRow("密码:", self.password_edit)

        layout.addLayout(form_layout)

        # 定时获取设置
        timer_group = QGroupBox("定时获取设置")
        timer_layout = QVBoxLayout()

        # 启用定时获取
        self.enable_timer_cb = QCheckBox("启用定时从网管获取")
        timer_layout.addWidget(self.enable_timer_cb)

        # 获取间隔设置
        from PySide6.QtWidgets import QSpinBox
        interval_layout = QHBoxLayout()
        interval_label = QLabel("获取间隔:")
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setMinimum(1)
        self.interval_spinbox.setMaximum(1440)  # 最大24小时
        self.interval_spinbox.setValue(10)  # 默认10分钟
        self.interval_spinbox.setSuffix(" 分钟")

        interval_layout.addWidget(interval_label)
        interval_layout.addWidget(self.interval_spinbox)
        interval_layout.addStretch()

        timer_layout.addLayout(interval_layout)

        # 时间限制设置
        time_limit_layout = QVBoxLayout()

        # 启用时间限制
        self.enable_time_limit_cb = QCheckBox("启用时间限制（避免夜间获取）")
        time_limit_layout.addWidget(self.enable_time_limit_cb)

        # 时间范围设置
        time_range_layout = QHBoxLayout()
        time_range_layout.addWidget(QLabel("限制时间段:"))

        from PySide6.QtWidgets import QTimeEdit
        from PySide6.QtCore import QTime

        self.start_time_edit = QTimeEdit()
        self.start_time_edit.setTime(QTime(23, 59))
        self.start_time_edit.setDisplayFormat("HH:mm")

        time_range_layout.addWidget(self.start_time_edit)
        time_range_layout.addWidget(QLabel("至"))

        self.end_time_edit = QTimeEdit()
        self.end_time_edit.setTime(QTime(6, 0))
        self.end_time_edit.setDisplayFormat("HH:mm")

        time_range_layout.addWidget(self.end_time_edit)
        time_range_layout.addStretch()

        time_limit_layout.addLayout(time_range_layout)

        # 时间限制说明
        time_limit_info = QLabel("在指定时间段内，定时获取将被跳过，避免夜间对网管系统造成负载。")
        time_limit_info.setWordWrap(True)
        time_limit_info.setStyleSheet("color: #666; font-size: 10px;")
        time_limit_layout.addWidget(time_limit_info)

        timer_layout.addLayout(time_limit_layout)

        # 说明文本
        info_label = QLabel("注意：定时获取会定期从网管系统抓取最新数据，请合理设置间隔时间。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 10px;")
        timer_layout.addWidget(info_label)

        timer_group.setLayout(timer_layout)
        layout.addWidget(timer_group)

        # 重点告警设置
        focus_group = QGroupBox("重点告警配置")
        focus_layout = QVBoxLayout()

        # 说明标签
        focus_info_label = QLabel("配置重点关注的告警关键字（每行一个）：")
        focus_layout.addWidget(focus_info_label)

        # 关键字输入框
        from PySide6.QtWidgets import QTextEdit
        self.focus_keywords_edit = QTextEdit()
        self.focus_keywords_edit.setMaximumHeight(100)
        self.focus_keywords_edit.setPlaceholderText("例如：\nCPU过载\n外部扩展设备故障\n时钟源异常")
        focus_layout.addWidget(self.focus_keywords_edit)

        # 提示信息
        focus_tip_label = QLabel("提示：包含这些关键字的告警将显示🎯重点标记")
        focus_tip_label.setStyleSheet("color: #666; font-size: 10px;")
        focus_layout.addWidget(focus_tip_label)

        focus_group.setLayout(focus_layout)
        layout.addWidget(focus_group)

        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        # 加载配置
        self.load_config()

    def load_config(self):
        """加载配置"""
        try:
            config = configparser.ConfigParser()
            if os.path.exists(CONFIG_FILE):
                config.read(CONFIG_FILE, encoding='utf-8')
                self.username_edit.setText(config.get('login', 'username', fallback=''))
                self.password_edit.setText(config.get('login', 'password', fallback=''))

                # 加载定时器设置
                self.enable_timer_cb.setChecked(config.getboolean('timer', 'enabled', fallback=False))
                self.interval_spinbox.setValue(config.getint('timer', 'interval_minutes', fallback=10))

                # 加载时间限制设置
                self.enable_time_limit_cb.setChecked(config.getboolean('time_limit', 'enabled', fallback=False))
                start_time_str = config.get('time_limit', 'start_time', fallback='23:59')
                end_time_str = config.get('time_limit', 'end_time', fallback='06:00')

                # 解析时间字符串并设置到时间编辑器
                from PySide6.QtCore import QTime
                try:
                    start_hour, start_minute = map(int, start_time_str.split(':'))
                    self.start_time_edit.setTime(QTime(start_hour, start_minute))
                except:
                    self.start_time_edit.setTime(QTime(23, 59))

                try:
                    end_hour, end_minute = map(int, end_time_str.split(':'))
                    self.end_time_edit.setTime(QTime(end_hour, end_minute))
                except:
                    self.end_time_edit.setTime(QTime(6, 0))

                # 加载重点告警关键字
                focus_keywords = config.get('focus', 'keywords', fallback='CPU过载\n外部扩展设备故障\n时钟源异常')
                self.focus_keywords_edit.setPlainText(focus_keywords)
        except:
            pass

    def save_config(self):
        """保存配置"""
        try:
            config = configparser.ConfigParser()
            config['login'] = {
                'username': self.username_edit.text(),
                'password': self.password_edit.text()
            }
            config['timer'] = {
                'enabled': str(self.enable_timer_cb.isChecked()),
                'interval_minutes': str(self.interval_spinbox.value())
            }
            config['time_limit'] = {
                'enabled': str(self.enable_time_limit_cb.isChecked()),
                'start_time': self.start_time_edit.time().toString('HH:mm'),
                'end_time': self.end_time_edit.time().toString('HH:mm')
            }
            config['focus'] = {
                'keywords': self.focus_keywords_edit.toPlainText()
            }
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)
        except Exception as e:
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", f"保存配置失败: {e}")

    def get_credentials(self):
        """获取凭据"""
        return self.username_edit.text(), self.password_edit.text()

    def get_timer_config(self):
        """获取定时器配置"""
        return self.enable_timer_cb.isChecked(), self.interval_spinbox.value()

class AlarmFetcher:
    """完整的告警抓取器 - 集成web.py的所有功能"""

    def __init__(self, username, password, db_file, headless=True):
        self.username = username
        self.password = password
        self.db_file = db_file
        self.headless = headless  # 是否无头模式
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None  # 添加 playwright 实例引用
        self.api_url = None
        self.request_payload = None
        self.sniffed_headers = None
        self._log_callback = None
        self._stop_requested = False  # 添加停止标志

    async def fetch_all_alarms(self, progress_callback=None, log_callback=None):
        """完整的告警抓取流程"""
        try:
            # 设置回调
            self._log_callback = log_callback

            # 检查停止标志
            if self._stop_requested:
                if log_callback:
                    log_callback("⏹️ 抓取已被停止")
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            # 0. 创建数据库结构
            if log_callback:
                log_callback("📄 创建数据库结构...")
            create_database_structure(self.db_file)

            # 1. 启动浏览器
            if self._stop_requested:
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            if log_callback:
                log_callback("🚀 启动浏览器...")
            if progress_callback:
                progress_callback(10, "启动浏览器...")

            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=['--ignore-certificate-errors', '--ignore-ssl-errors', '--ignore-certificate-errors-spki-list']
            )

            # 创建上下文，忽略SSL证书错误
            self.context = await self.browser.new_context(
                ignore_https_errors=True,  # 忽略HTTPS证书错误
                accept_downloads=False,
                java_script_enabled=True
            )

            self.page = await self.context.new_page()

            # 2. 登录网管系统
            if self._stop_requested:
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            if log_callback:
                log_callback("🔐 登录网管系统...")
            if progress_callback:
                progress_callback(20, "登录网管系统...")

            await self._login()

            # 3. 进入告警页面
            if self._stop_requested:
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            if log_callback:
                log_callback("📱 进入告警页面...")
            if progress_callback:
                progress_callback(30, "进入告警页面...")

            await self._navigate_to_alarm_page()

            # 4. 嗅探API接口
            if self._stop_requested:
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            if log_callback:
                log_callback("🔍 嗅探API接口...")
            if progress_callback:
                progress_callback(40, "嗅探API接口...")

            await self._sniff_api()

            # 5. 抓取告警数据
            if self._stop_requested:
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            if log_callback:
                log_callback("📡 开始抓取告警数据...")
            if progress_callback:
                progress_callback(50, "抓取告警数据...")

            alarms = await self._fetch_alarm_data(progress_callback, log_callback)

            # 6. 同步到数据库
            if self._stop_requested:
                return {'current_active_count': 0, 'new_count': 0, 'continuing_count': 0, 'cleared_count': 0}

            if log_callback:
                log_callback("💾 同步数据到数据库...")
            if progress_callback:
                progress_callback(90, "同步数据库...")

            stats = await self._sync_to_database(alarms, log_callback)

            if progress_callback:
                progress_callback(100, "抓取完成")

            return stats

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 抓取失败: {e}")
            raise e
        finally:
            await self._cleanup()

    async def _login(self):
        """登录网管系统 - 直接从web.py复制的完美工作版本"""
        if log_callback := getattr(self, '_log_callback', None):
            log_callback(f"打开登录页: {DEFAULT_LOGIN_URL}")

        await self.page.goto(DEFAULT_LOGIN_URL, timeout=30000)
        await self.page.wait_for_load_state("networkidle")
        await self.page.wait_for_timeout(1500)

        await self.page.wait_for_selector(LOGIN_SELECTORS["username_input"], timeout=30000)
        await self.page.fill(LOGIN_SELECTORS["username_input"], self.username)
        await self.page.fill(LOGIN_SELECTORS["password_input"], self.password)
        await self.page.click(LOGIN_SELECTORS["login_button"])
        await self.page.wait_for_timeout(3500)

        if log_callback := getattr(self, '_log_callback', None):
            log_callback(f"登录后 URL: {self.page.url}")

    async def _navigate_to_alarm_page(self):
        """进入告警页面 - 直接从web.py复制的完美工作版本"""
        if log_callback := getattr(self, '_log_callback', None):
            log_callback(f"进入当前告警页: {DEFAULT_ALARM_URL}")

        await self.page.goto(DEFAULT_ALARM_URL, timeout=40000)
        await self.page.wait_for_load_state("networkidle")
        await self.page.wait_for_timeout(2000)

        try:
            await self.page.click(MENU_SELECTORS["alarm_menu"], timeout=2000)
            await self.page.wait_for_timeout(1000)
            await self.page.click(MENU_SELECTORS["current_alarm"], timeout=2000)
            await self.page.wait_for_timeout(2500)
            if log_callback := getattr(self, '_log_callback', None):
                log_callback("已通过菜单兜底进入'当前告警'页面。")
        except Exception:
            pass

    async def _sniff_api(self):
        """嗅探API接口 - 直接从web.py复制的完美工作版本"""
        if log_callback := getattr(self, '_log_callback', None):
            log_callback("开始嗅探表格请求体 ...")

        # 确保页面完全加载
        try:
            await self.page.wait_for_load_state("networkidle", timeout=10000)
            await self.page.wait_for_load_state("domcontentloaded", timeout=5000)
            if log_callback := getattr(self, '_log_callback', None):
                log_callback("页面加载状态检查完成")
        except Exception as e:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback(f"页面加载状态检查超时: {str(e)}")

        await self.page.wait_for_timeout(2000)

        async def _try_triggers():
            triggered_count = 0
            for sel in REFRESH_TRIGGERS:
                try:
                    el = await self.page.query_selector(sel)
                    if el:
                        if log_callback := getattr(self, '_log_callback', None):
                            log_callback(f"尝试点击触发器: {sel}")
                        await el.click()
                        await self.page.wait_for_timeout(1200)
                        triggered_count += 1
                        if triggered_count >= 3:
                            break
                except Exception as e:
                    if log_callback := getattr(self, '_log_callback', None):
                        log_callback(f"触发器 {sel} 点击失败: {str(e)}")
                    continue
            if log_callback := getattr(self, '_log_callback', None):
                log_callback(f"本次触发了 {triggered_count} 个元素")

        if log_callback := getattr(self, '_log_callback', None):
            log_callback("第一次尝试嗅探...")
        await _try_triggers()

        def _pred(req) -> bool:
            try:
                url = req.url.lower()
                return req.method == "POST" and any(s in url for s in API_URL_SUBSTRINGS)
            except Exception:
                return False

        try:
            req = await self.page.wait_for_event("request", predicate=_pred, timeout=45000)
        except Exception as e1:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback("第一次嗅探失败，进行第二次尝试...")
            await self.page.wait_for_timeout(2000)
            await _try_triggers()
            try:
                req = await self.page.wait_for_event("request", predicate=_pred, timeout=22500)
            except Exception as e2:
                if log_callback := getattr(self, '_log_callback', None):
                    log_callback("第二次嗅探失败，进行第三次尝试...")
                await self.page.wait_for_timeout(3000)
                await _try_triggers()
                try:
                    req = await self.page.wait_for_event("request", predicate=_pred, timeout=15000)
                except Exception as e3:
                    raise Exception("未能嗅探到任何'当前告警'接口请求")

        # 解析请求
        self.api_url = req.url
        headers = dict(req.headers)

        body_text = req.post_data
        if not body_text:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback("已捕获到请求，但 post_data 为空。")
            self.request_payload = {}
        else:
            try:
                self.request_payload = json.loads(body_text)
            except Exception:
                if log_callback := getattr(self, '_log_callback', None):
                    log_callback("捕获到的请求体不是有效 JSON，使用默认结构。")
                self.request_payload = {
                    "pageNum": 1,
                    "pageSize": 500,
                    "sortField": "alarmRaisedTime",
                    "sortOrder": "desc"
                }

        if log_callback := getattr(self, '_log_callback', None):
            log_callback(f"已捕获真实接口: {self.api_url}")

        self.sniffed_headers = headers

    async def _fetch_alarm_data(self, progress_callback=None, log_callback=None):
        """分页抓取告警数据 - 直接从web.py复制的完美版本"""
        import copy
        from datetime import timezone

        all_alarms = []
        body = copy.deepcopy(self.request_payload) if isinstance(self.request_payload, dict) else {}

        # 设置分页参数
        body["pagesize"] = 500
        body["page"] = 1
        body.pop("queryid", None)

        queryid = None
        page_no = 1
        max_alarms = 50000

        while len(all_alarms) < max_alarms:
            # 检查停止标志
            if self._stop_requested:
                if log_callback:
                    log_callback(f"⏹️ 抓取被停止，已获取 {len(all_alarms)} 条数据")
                break

            body["page"] = page_no
            body["timestamp"] = datetime.now(timezone.utc).isoformat(timespec="milliseconds").replace("+00:00", "Z")
            if queryid:
                body["queryid"] = queryid

            # 使用web.py的请求方式
            try:
                hdrs = {
                    "Accept": "application/json, text/plain, */*",
                    "Content-Type": "application/json",
                }
                if self.sniffed_headers:
                    for k, v in self.sniffed_headers.items():
                        lk = k.lower()
                        if lk not in ["content-length", "host"]:
                            hdrs[k] = v

                response = await self.context.request.post(
                    self.api_url,
                    data=json.dumps(body),
                    headers=hdrs
                )

                if response.status == 403:
                    if log_callback:
                        log_callback(f"第{page_no}页请求失败，状态码: 403 (权限不足)")
                    break
                elif response.status != 200:
                    if log_callback:
                        log_callback(f"第{page_no}页请求失败，状态码: {response.status}")
                    break

                data = await response.json()

                if isinstance(data, dict):
                    rows = data.get("alarms") or data.get("rows") or []
                    if not isinstance(rows, list):
                        if log_callback:
                            log_callback(f"第{page_no}页返回的 alarms/rows 不是列表，终止。")
                        break

                    if log_callback:
                        log_callback(f"第{page_no}页获取 {len(rows)} 条")

                    all_alarms.extend(rows)

                    if not queryid:
                        queryid = data.get("queryid")

                    if progress_callback:
                        progress = 50 + (page_no * 30 / 50)  # 假设最多50页
                        progress_callback(int(progress), f"第{page_no}页...")

                    # 如果返回数据少于页大小，说明是最后一页
                    if len(rows) < body["pagesize"]:
                        break
                else:
                    if log_callback:
                        log_callback(f"第{page_no}页返回数据格式异常，终止。")
                    break

                page_no += 1

                # 防止无限循环
                if page_no > 100:
                    break

            except Exception as e:
                if log_callback:
                    log_callback(f"第{page_no}页请求异常: {e}")
                break

        if log_callback:
            log_callback(f"✅ 抓取完成，共 {len(all_alarms)} 条")

        return all_alarms

    async def _sync_to_database(self, alarms, log_callback=None):
        """同步数据到数据库 - 直接从web.py复制的完美版本"""
        return self._snapshot_sync_alarms(alarms, log_callback)

    def _snapshot_sync_alarms(self, alarms_data, log_callback=None):
        """快照式同步：Web显示 = 网管当前状态 - 直接从web.py复制"""
        current_time = datetime.now().isoformat()

        # 如果没有抓取到数据，直接返回
        if not alarms_data:
            if log_callback:
                log_callback("本次抓取: 0 条告警")
            return {
                'current_active_count': 0,
                'new_count': 0,
                'continuing_count': 0,
                'cleared_count': 0,
                'is_first_fetch': True
            }

        # === 数据预处理和去重分析 ===
        if log_callback:
            log_callback(f"📊 开始数据预处理，原始数据: {len(alarms_data)} 条")

        # 统计去重信息
        duplicate_info = {
            'total_raw': len(alarms_data),
            'no_alarm_key': [],
            'duplicates': [],
            'valid_unique': {}
        }

        # 处理数据，检测重复和无效记录
        seen_keys = {}
        for i, alarm in enumerate(alarms_data):
            alarm_key = alarm.get('alarmkey', '')

            if not alarm_key:
                # 记录缺少alarm_key的告警
                duplicate_info['no_alarm_key'].append({
                    'index': i + 1,
                    'alarm_name': alarm.get('codename', '未知'),
                    'me_name': alarm.get('mename', '未知'),
                    'ne_ip': alarm.get('neip', '未知')
                })
                continue

            # 检查完整的alarm_key是否重复（不是部分匹配）
            if alarm_key in seen_keys:
                # 记录重复的告警 - 保存两个告警的完整Key
                first_alarm = seen_keys[alarm_key]['alarm']
                first_key = seen_keys[alarm_key]['alarm'].get('alarmkey', '')

                # 调试信息：检查两个key是否真的相同
                if log_callback:
                    log_callback(f"🔍 发现重复Key调试:")
                    log_callback(f"    第{seen_keys[alarm_key]['index']}条Key: {first_key}")
                    log_callback(f"    第{i + 1}条Key: {alarm_key}")
                    log_callback(f"    Key相等: {first_key == alarm_key}")

                duplicate_info['duplicates'].append({
                    'first_key': first_key,
                    'duplicate_key': alarm_key,
                    'first_index': seen_keys[alarm_key]['index'],
                    'duplicate_index': i + 1,
                    'first_alarm': {
                        'alarm_name': first_alarm.get('codename', '未知'),
                        'me_name': first_alarm.get('mename', '未知'),
                        'alarm_time': first_alarm.get('alarmraisedtime', '未知'),
                        'position': first_alarm.get('positionname', '未知')
                    },
                    'duplicate_alarm': {
                        'alarm_name': alarm.get('codename', '未知'),
                        'me_name': alarm.get('mename', '未知'),
                        'alarm_time': alarm.get('alarmraisedtime', '未知'),
                        'position': alarm.get('positionname', '未知')
                    }
                })
            else:
                # 记录有效的唯一告警
                seen_keys[alarm_key] = {
                    'index': i + 1,
                    'alarm': alarm
                }
                duplicate_info['valid_unique'][alarm_key] = alarm

        # 打印去重详情
        self._print_deduplication_details(duplicate_info, log_callback)

        # === 事务开始 ===
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        try:
            # 1. 获取上次活跃的告警键
            cursor.execute("SELECT alarm_key FROM alarms WHERE is_active = 1")
            last_active_keys = set(row[0] for row in cursor.fetchall())

            # 2. 使用去重后的数据
            current_keys = set(duplicate_info['valid_unique'].keys())
            alarm_dict = duplicate_info['valid_unique']

            # 3. 分析状态变化
            is_first_fetch = len(last_active_keys) == 0

            if is_first_fetch:
                # 首次爬取：建立基线，不统计状态变化
                new_alarms = set()  # 首次不算新增
                continuing_alarms = current_keys  # 全部算作基线数据
                cleared_alarms = set()  # 首次不算清除

                if log_callback:
                    log_callback(f"🎯 首次爬取建立基线: {len(current_keys)} 条活跃告警")
                    log_callback(f"📊 基线建立完成，下次爬取将开始统计状态变化")
            else:
                # 正常爬取：统计状态变化
                new_alarms = current_keys - last_active_keys          # 🆕 新出现的
                continuing_alarms = current_keys & last_active_keys   # 📍 持续存在的
                cleared_alarms = last_active_keys - current_keys      # ❌ 已消失的

                if log_callback:
                    log_callback(f"网管当前状态: {len(current_keys)} 条活跃告警")
                    log_callback(f"🆕 新出现: {len(new_alarms)} 条")
                    log_callback(f"📍 持续存在: {len(continuing_alarms)} 条")
                    log_callback(f"❌ 已消失: {len(cleared_alarms)} 条")

            # 4. 快照式同步：先将所有告警标记为非活跃
            cursor.execute("UPDATE alarms SET is_active = 0, last_seen_at = ?", (current_time,))

            # 5. 处理本次抓取的告警（使用去重后的数据）- 完全对齐web.py逻辑
            for alarm_key, alarm in alarm_dict.items():
                # 确定告警状态 - 完全对齐web.py逻辑
                if is_first_fetch:
                    # 首次抓取：设置为基线数据
                    is_new_value = 0  # 基线数据不是"新的"
                    is_baseline_value = 1  # 标记为基线数据
                else:
                    # 正常抓取：基于集合运算确定状态
                    is_new_value = 1 if alarm_key in new_alarms else 0
                    is_baseline_value = 0  # 不是基线数据

                result = self._insert_alarm_data(cursor, alarm, is_new_value, is_baseline_value)

            # 6. 统计最终结果
            cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1")
            final_active_count = cursor.fetchone()[0]

            if log_callback:
                log_callback(f"同步完成: Web将显示 {final_active_count} 条活跃告警")

            conn.commit()

            return {
                'current_active_count': final_active_count,
                'new_count': len(new_alarms),
                'continuing_count': len(continuing_alarms),
                'cleared_count': len(cleared_alarms),
                'is_first_fetch': is_first_fetch
            }

        except Exception as e:
            if log_callback:
                log_callback(f"快照同步期间发生错误: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def _insert_alarm_data(self, cursor, alarm_data, is_new=None, is_baseline=None):
        """将告警数据插入数据库 - 完全对齐web.py逻辑"""
        alarm_key = alarm_data.get('alarmkey', '')
        if not alarm_key:
            print(f"警告: 告警记录缺少alarm_key，跳过处理")
            return False

        # 检查告警是否已存在（基于alarm_key）
        cursor.execute("SELECT id, is_new, first_seen_at FROM alarms WHERE alarm_key = ?", (alarm_key,))
        existing = cursor.fetchone()

        current_time = datetime.now().isoformat()

        # 提取告警字段
        def safe_get(data, key, default=None):
            """安全获取字段值"""
            value = data.get(key, default)
            if isinstance(value, dict):
                return json.dumps(value, ensure_ascii=False)
            return value

        # 构建字段映射
        fields = {
            'code_name': safe_get(alarm_data, 'codename'),
            'perceived_severity_name': safe_get(alarm_data, 'perceivedseverityname'),
            'ack_state_name': safe_get(alarm_data, 'ackstatename'),
            'me_name': safe_get(alarm_data, 'mename'),
            'alarm_raised_time': safe_get(alarm_data, 'alarmraisedtime'),
            'alarm_type_name': safe_get(alarm_data, 'alarmtypename'),
            'reason_name': safe_get(alarm_data, 'reasonname'),
            'ne_ip': safe_get(alarm_data, 'neip'),
            'additional_text': safe_get(alarm_data, 'additionaltext'),
            'position_name': safe_get(alarm_data, 'positionname'),
            'alarm_code': safe_get(alarm_data, 'alarmcode'),
            'res_type_name': safe_get(alarm_data, 'restypename'),
            'clear_state_name': safe_get(alarm_data, 'clearstatename'),
            'ack_user_id': safe_get(alarm_data, 'ackuserid'),
            'comment_text': safe_get(alarm_data, 'commenttext'),
            'raw_data': json.dumps(alarm_data, ensure_ascii=False)
        }

        if existing:
            # 更新现有记录 - 使用传递的状态值
            final_is_new = is_new if is_new is not None else 0
            final_is_baseline = is_baseline if is_baseline is not None else 0

            update_sql = """
                UPDATE alarms SET
                    code_name = ?, perceived_severity_name = ?, ack_state_name = ?,
                    me_name = ?, alarm_raised_time = ?, alarm_type_name = ?,
                    reason_name = ?, ne_ip = ?, additional_text = ?,
                    position_name = ?, alarm_code = ?, res_type_name = ?,
                    clear_state_name = ?, ack_user_id = ?, comment_text = ?,
                    raw_data = ?, is_active = 1, is_new = ?, is_baseline = ?, last_seen_at = ?
                WHERE alarm_key = ?
            """

            cursor.execute(update_sql, (
                fields['code_name'], fields['perceived_severity_name'], fields['ack_state_name'],
                fields['me_name'], fields['alarm_raised_time'], fields['alarm_type_name'],
                fields['reason_name'], fields['ne_ip'], fields['additional_text'],
                fields['position_name'], fields['alarm_code'], fields['res_type_name'],
                fields['clear_state_name'], fields['ack_user_id'], fields['comment_text'],
                fields['raw_data'], final_is_new, final_is_baseline, current_time, alarm_key
            ))

            result = {'action': 'updated', 'is_new': final_is_new}
        else:
            # 插入新记录 - 使用传递的状态值
            final_is_new = is_new if is_new is not None else 1
            final_is_baseline = is_baseline if is_baseline is not None else 0

            insert_sql = """
                INSERT INTO alarms (
                    alarm_key, code_name, perceived_severity_name, ack_state_name,
                    me_name, alarm_raised_time, alarm_type_name, reason_name,
                    ne_ip, additional_text, position_name, alarm_code,
                    res_type_name, clear_state_name, ack_user_id, comment_text,
                    raw_data, is_active, is_new, is_baseline, first_seen_at, last_seen_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?)
            """

            cursor.execute(insert_sql, (
                alarm_key, fields['code_name'], fields['perceived_severity_name'], fields['ack_state_name'],
                fields['me_name'], fields['alarm_raised_time'], fields['alarm_type_name'], fields['reason_name'],
                fields['ne_ip'], fields['additional_text'], fields['position_name'], fields['alarm_code'],
                fields['res_type_name'], fields['clear_state_name'], fields['ack_user_id'], fields['comment_text'],
                fields['raw_data'], final_is_new, final_is_baseline, current_time, current_time
            ))

            result = {'action': 'inserted', 'is_new': final_is_new}

        return result

    def _print_deduplication_details(self, duplicate_info, log_callback=None):
        """打印去重详情"""
        if not log_callback:
            return

        total_raw = duplicate_info['total_raw']
        no_key_count = len(duplicate_info['no_alarm_key'])
        duplicate_count = len(duplicate_info['duplicates'])
        valid_count = len(duplicate_info['valid_unique'])

        log_callback("=" * 60)
        log_callback("📊 数据去重详情报告")
        log_callback("=" * 60)
        log_callback(f"📥 原始数据总数: {total_raw} 条")
        log_callback(f"✅ 有效唯一数据: {valid_count} 条")
        log_callback(f"❌ 被过滤数据: {no_key_count + duplicate_count} 条")

        if no_key_count > 0:
            log_callback(f"\n🚫 缺少alarm_key的告警 ({no_key_count}条):")
            for item in duplicate_info['no_alarm_key']:
                log_callback(f"  第{item['index']}条: {item['alarm_name']} | {item['me_name']} | {item['ne_ip']}")

        if duplicate_count > 0:
            log_callback(f"\n🔄 重复的告警 ({duplicate_count}条):")
            for item in duplicate_info['duplicates']:
                log_callback(f"  重复: 第{item['first_index']}条 vs 第{item['duplicate_index']}条")
                log_callback(f"    告警: {item['first_alarm']['alarm_name']}")
                log_callback(f"    网元: {item['first_alarm']['me_name']}")
                log_callback(f"    发生时间: {self._format_alarm_time(item['first_alarm']['alarm_time'])}")
                log_callback(f"    位置: {item['first_alarm']['position']}")
                log_callback(f"    Key1: {item['first_key']}")
                log_callback(f"    Key2: {item['duplicate_key']}")
                log_callback("")

        if no_key_count == 0 and duplicate_count == 0:
            log_callback("✨ 数据质量优秀，无重复或无效记录！")

        log_callback("=" * 60)

    def _format_alarm_time(self, alarm_time):
        """格式化告警时间"""
        try:
            if isinstance(alarm_time, (int, float)) and alarm_time > 0:
                # 假设是毫秒时间戳
                if alarm_time > 1000000000000:  # 毫秒时间戳
                    timestamp = alarm_time / 1000
                else:  # 秒时间戳
                    timestamp = alarm_time

                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            else:
                return str(alarm_time)
        except:
            return str(alarm_time)

    def _parse_parent_info(self, raw_data):
        """解析parentinfo字段，提取根源告警的alarmkey"""
        try:
            parentinfo = raw_data.get('parentinfo', {})
            for key, value in parentinfo.items():
                if key.startswith('relation_') and value:
                    # value是数组，取第一个元素
                    relation_str = value[0] if isinstance(value, list) else value
                    # 按@分割，取第一段作为根源alarmkey
                    parts = relation_str.split('@')
                    if parts and parts[0]:
                        print(f"解析到根源alarmkey: {parts[0][:50]}...")
                        return parts[0]  # 根源的alarmkey
        except Exception as e:
            print(f"解析parentinfo失败: {e}")
        return None



    async def _cleanup(self):
        """清理资源 - 修复 Playwright 资源泄漏"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
        except Exception as e:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback(f"⚠️ 关闭页面时出错: {e}")

        try:
            if self.context:
                await self.context.close()
                self.context = None
        except Exception as e:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback(f"⚠️ 关闭上下文时出错: {e}")

        try:
            if self.browser:
                await self.browser.close()
                self.browser = None
        except Exception as e:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback(f"⚠️ 关闭浏览器时出错: {e}")

        # 🔧 修复：清理 Playwright 实例，防止进程和文件残留
        try:
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
                if log_callback := getattr(self, '_log_callback', None):
                    log_callback("🧹 Playwright 实例已清理")
        except Exception as e:
            if log_callback := getattr(self, '_log_callback', None):
                log_callback(f"⚠️ 清理 Playwright 实例时出错: {e}")

        if log_callback := getattr(self, '_log_callback', None):
            log_callback("🌐 浏览器资源清理完成")

class WebFetchWorker(QThread):
    """网管数据获取工作线程 - 完全内置的抓取逻辑"""

    finished_ok = Signal(dict)     # 成功信号，返回统计信息
    finished_err = Signal(str)     # 失败信号，返回错误信息
    log_message = Signal(str)      # 日志信号
    progress_update = Signal(int, str)  # 进度信号 (百分比, 描述)

    def __init__(self, username, password, db_file, headless=True):
        super().__init__()
        self.username = username
        self.password = password
        self.db_file = db_file
        self.headless = headless
        self._stop_requested = False
        self.fetcher = None

    def stop(self):
        """请求停止"""
        self._stop_requested = True
        # 如果有活跃的抓取器，尝试清理其资源
        if hasattr(self, 'fetcher') and self.fetcher:
            try:
                # 设置停止标志，让正在运行的抓取尽快退出
                if hasattr(self.fetcher, '_stop_requested'):
                    self.fetcher._stop_requested = True
            except Exception:
                pass

    def run(self):
        """执行网管数据获取"""
        if not PLAYWRIGHT_AVAILABLE:
            self.finished_err.emit("Playwright未安装，无法执行抓取功能。\n请运行: pip install playwright\n然后运行: playwright install")
            return

        try:
            # 创建抓取器
            self.fetcher = AlarmFetcher(self.username, self.password, self.db_file, self.headless)

            # 运行异步抓取
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            stats = loop.run_until_complete(
                self.fetcher.fetch_all_alarms(
                    progress_callback=self._on_progress,
                    log_callback=self._on_log
                )
            )

            loop.close()

            # 发送成功信号
            self.finished_ok.emit(stats)

        except Exception as e:
            error_msg = str(e)
            self.log_message.emit(f"❌ 抓取失败: {error_msg}")

            # 处理常见错误
            if "登录" in error_msg:
                self.finished_err.emit("登录失败，请检查用户名和密码")
            elif "网络" in error_msg or "timeout" in error_msg.lower():
                self.finished_err.emit("网络连接失败，请检查网管系统地址或VPN连接")
            elif "playwright" in error_msg.lower():
                self.finished_err.emit("浏览器组件未安装，请运行: playwright install")
            else:
                self.finished_err.emit(f"抓取失败: {error_msg}")

    def _on_progress(self, percentage, description):
        """进度回调"""
        if not self._stop_requested:
            self.progress_update.emit(percentage, description)

    def _on_log(self, message):
        """日志回调"""
        if not self._stop_requested:
            self.log_message.emit(message)

class DataWorker(QThread):
    """数据加载工作线程"""
    finished_ok = Signal(list, dict)  # 正常完成信号
    finished_err = Signal(str)        # 异常完成信号
    log_message = Signal(str)
    progress_update = Signal(int)     # 进度信号

    def __init__(self, db_file, focus_keywords=None):
        super().__init__()
        self.db_file = db_file
        self.focus_keywords = focus_keywords or DEFAULT_FOCUS_KEYWORDS
        self._stop_requested = False

    def determine_operator(self, plmn_display_info):
        """根据PLMN显示信息判断运营商"""
        try:
            if not plmn_display_info:
                return "联通"  # 空值默认为联通

            plmn_str = str(plmn_display_info).strip()

            # 只有单独的460-11才是电信，其他都是联通
            if plmn_str == "460-11":
                return "电信"
            else:
                # 所有其他情况（包括混合PLMN、其他单独PLMN等）都是联通
                return "联通"

        except Exception as e:
            print(f"判断运营商失败: {e}")
            return "联通"  # 异常时默认为联通

    def build_alarm_key_mapping(self, raw_alarms_data):
        """建立 key → 告警 的映射，同时支持 alarm_key 和 raw_data.alarmkey"""
        key_to_alarm = {}
        total_count = 0
        db_key_count = 0
        raw_key_count = 0

        for alarm in raw_alarms_data:
            total_count += 1
            db_key = alarm.get('alarm_key') or ''
            raw_key = alarm.get('alarmkey') or ''

            # 前3个样本打印一次，便于核验
            if total_count <= 3:
                print(f"样本{total_count} alarm_key(db): '{db_key}'  alarmkey(raw): '{raw_key}'")

            if db_key:
                key_to_alarm[db_key] = alarm
                db_key_count += 1
            if raw_key:
                key_to_alarm[raw_key] = alarm
                raw_key_count += 1

        print(f"建立映射完成: 共{len(key_to_alarm)}个键 → {total_count}条告警 (db_key:{db_key_count}, raw_key:{raw_key_count})")
        return key_to_alarm

    def _normalize_parentinfo(self, raw_data):
        """把 raw_data.parentinfo 规范成 dict；支持字符串/None/畸形输入。"""
        parentinfo = {}
        try:
            pi = (raw_data or {}).get('parentinfo', {})
            if isinstance(pi, str):
                try:
                    pi = json.loads(pi) if pi.strip() else {}
                except Exception:
                    pi = {}
            if isinstance(pi, dict):
                parentinfo = pi
        except Exception:
            parentinfo = {}
        return parentinfo

    def _pointers_from_parentinfo(self, raw_data):
        """
        按优先级取父指针数组：
        1) relation_YYYY_MM（取子告警发生时间所在月）
        2) relation
        3) 其它所有 relation_* 的并集（去重，保持原顺序）
        """
        pi = self._normalize_parentinfo(raw_data)
        if not pi:
            return []

        # 先尝试按月份精确命中
        tm = (raw_data or {}).get('alarmraisedtime')
        prefer_key = None
        try:
            if tm:
                # 支持秒或毫秒时间戳
                ts = float(tm)
                if ts > 1e12: ts /= 1000.0
                from datetime import datetime
                ym = datetime.fromtimestamp(ts).strftime('%Y_%m')
                prefer_key = f'relation_{ym}'
        except Exception:
            prefer_key = None

        def _as_list(v):
            if v is None:
                return []
            if isinstance(v, list):
                return v
            return [v]

        # 1) relation_YYYY_MM
        if prefer_key and pi.get(prefer_key):
            return _as_list(pi.get(prefer_key))

        # 2) relation
        if pi.get('relation'):
            return _as_list(pi.get('relation'))

        # 3) 并集
        seen = set()
        out = []
        for k, v in pi.items():
            if k.startswith('relation_') and v:
                for it in _as_list(v):
                    if it not in seen:
                        seen.add(it)
                        out.append(it)
        return out

    def _parse_parent_pointer(self, ptr):
        """
        指针规范：5段，用 @ 分割：
        0=root_alarmkey, 1=组前缀ID, 2=父服务器时间, 3=父告警ID, 4=保留/其它
        返回 dict，格式不合法则返回 None。
        """
        try:
            s = str(ptr).strip()
            parts = s.split('@')
            if len(parts) < 5:
                return None
            return {
                'root_alarmkey': parts[0],
                'group_prefix': parts[1],
                'parent_servertime': parts[2],
                'parent_alarm_id': parts[3],
                'extra': parts[4]
            }
        except Exception:
            return None

    def find_root_alarm(self, alarm, key_to_alarm):
        """只看 parentinfo，优先月份键，再 relation，再并集；含健壮校验与防环。"""
        try:
            current = alarm
            visited = set()
            path = []

            while True:
                raw_data = current.get('raw_data', {}) or {}
                relationflag = raw_data.get('relationflag', 0)
                alarmkey = current.get('alarm_key', '') or current.get('alarmkey', '')

                # 根源
                if relationflag == 1:
                    return {'root_alarm': current, 'path': path, 'success': True}

                if alarmkey in visited:
                    return {'root_alarm': None, 'path': path, 'success': False, 'error': '循环引用'}
                visited.add(alarmkey)

                path.append({'alarm': current, 'relationflag': relationflag, 'alarmkey': alarmkey})

                # 取父指针
                pointers = self._pointers_from_parentinfo(raw_data)
                parent_root_key = None
                for ptr in pointers:
                    meta = self._parse_parent_pointer(ptr)
                    if not meta:
                        continue
                    if meta['root_alarmkey']:
                        parent_root_key = meta['root_alarmkey']
                        break

                # 无父
                if not parent_root_key:
                    return {'root_alarm': None, 'path': path, 'success': False, 'error': '无父级信息或指针不合法'}

                # 映射里找父（或根）
                if parent_root_key not in key_to_alarm:
                    return {'root_alarm': None, 'path': path, 'success': False, 'error': f'父级不存在: {parent_root_key[:60]}...'}

                current = key_to_alarm[parent_root_key]

                if len(path) > 20:
                    return {'root_alarm': None, 'path': path, 'success': False, 'error': '追踪层级过深'}

        except Exception as e:
            return {'root_alarm': None, 'path': [], 'success': False, 'error': f'追踪失败: {e}'}

    def analyze_alarm_correlations(self, raw_alarms_data):
        """分析告警关联关系"""
        try:
            print("开始分析告警关联关系...")

            # 建立映射
            key_to_alarm = self.build_alarm_key_mapping(raw_alarms_data)

            # 调试：对前几条衍生/次根源做追根样本测试
            print("🔍 追根样本测试:")
            sample_count = 0
            for alarm in raw_alarms_data:
                if sample_count >= 5:
                    break
                raw_data = alarm.get('raw_data', {}) or {}
                relationflag = raw_data.get('relationflag', 0)
                if relationflag in (2, 3):  # 衍生或次根源
                    parentinfo = raw_data.get('parentinfo', {}) or {}
                    parent_key = parentinfo.get('relation', '')
                    print(f"  [追根样本{sample_count+1}] rf={relationflag}")
                    print(f"    parentinfo类型: {type(parentinfo)}")
                    if isinstance(parentinfo, dict):
                        print(f"    parentinfo字段: {list(parentinfo.keys())}")
                        print(f"    relation值: '{parent_key}'")
                        # 检查其他relation字段
                        for k, v in parentinfo.items():
                            if k.startswith('relation') and v:
                                print(f"    {k}: '{v}'")
                    else:
                        print(f"    parentinfo值: {parentinfo}")

                    result = self.find_root_alarm(alarm, key_to_alarm)
                    root_name = result.get('root_alarm', {}).get('code_name', '') if result.get('success') else ''
                    print(f"    追根结果: success={result.get('success')} root_name={root_name[:15]}...")
                    sample_count += 1

            # 检查是否有关联数据
            has_relation_data = False
            for alarm in raw_alarms_data[:10]:  # 检查前10个样本
                raw_data = alarm.get('raw_data', {})
                if isinstance(raw_data, dict) and ('relationflag' in raw_data or 'parentinfo' in raw_data):
                    has_relation_data = True
                    break

            # 调试：显示raw_data的实际内容
            print("🔍 检查raw_data内容:")
            for i, alarm in enumerate(raw_alarms_data[:3]):
                raw_data = alarm.get('raw_data', {})
                print(f"  样本{i+1} raw_data类型: {type(raw_data)}")
                if isinstance(raw_data, dict):
                    keys = list(raw_data.keys())[:10]
                    print(f"    包含字段: {keys}")
                    if 'relationflag' in raw_data:
                        print(f"    relationflag: {raw_data['relationflag']}")
                    if 'parentinfo' in raw_data:
                        parentinfo = raw_data['parentinfo']
                        print(f"    parentinfo类型: {type(parentinfo)}")
                        if isinstance(parentinfo, dict):
                            print(f"    parentinfo字段: {list(parentinfo.keys())}")
                            if 'relation' in parentinfo:
                                print(f"    parentinfo.relation: '{parentinfo['relation']}'")
                            # 检查其他relation字段
                            for k, v in parentinfo.items():
                                if k.startswith('relation'):
                                    print(f"    parentinfo.{k}: '{v}'")
                        else:
                            print(f"    parentinfo值: {parentinfo}")
                elif isinstance(raw_data, str):
                    print(f"    字符串长度: {len(raw_data)}")
                    print(f"    前100字符: {raw_data[:100]}")
                else:
                    print(f"    值: {raw_data}")

            if not has_relation_data:
                print("❌ 数据中没有关联信息（relationflag、parentinfo等）")
                print("💡 所有告警将被标记为独立告警")

                # 所有告警都是独立告警
                correlation_results = {}
                for alarm in raw_alarms_data:
                    alarmkey = alarm.get('alarm_key', '')
                    correlation_results[alarmkey] = {
                        'type': 'independent',
                        'root_name': '',
                        'path_length': 0
                    }

                print(f"关联分析完成:")
                print(f"  根源告警: 0 个")
                print(f"  次根源告警: 0 个")
                print(f"  衍生告警: 0 个")
                print(f"  独立告警: {len(raw_alarms_data)} 个")

                return correlation_results

            # 如果有关联数据，执行正常的关联分析
            stats = {'root': 0, 'sub_root': 0, 'derived': 0, 'independent': 0}
            correlation_results = {}

            # 添加进度跟踪（安全优化）
            total_alarms = len(raw_alarms_data)
            processed = 0

            for alarm in raw_alarms_data:
                # 每处理1000条检查一次停止标志（安全优化）
                if processed % 1000 == 0 and hasattr(self, '_stop_requested') and self._stop_requested:
                    print(f"🔗 关联分析被停止，已处理 {processed}/{total_alarms} 条")
                    break

                raw_data = alarm.get('raw_data', {})
                relationflag = raw_data.get('relationflag', 0)
                alarmkey = alarm.get('alarm_key', '')
                processed += 1

                if relationflag == 1:
                    stats['root'] += 1
                    correlation_results[alarmkey] = {
                        'type': 'root',
                        'root_name': '',
                        'path_length': 0,
                        'root_alarmkey': alarmkey  # 根源自己的唯一ID，供分组/展示
                    }
                elif relationflag == 2:
                    stats['derived'] += 1
                    # 查找根源
                    result = self.find_root_alarm(alarm, key_to_alarm)
                    if result['success']:
                        root_alarm = result['root_alarm']
                        root_name = root_alarm.get('code_name', '未知根源')
                        correlation_results[alarmkey] = {
                            'type': 'derived',
                            'root_name': root_name,
                            'path_length': len(result['path']),
                            'root_alarmkey': root_alarm.get('alarm_key', '')
                        }
                    else:
                        correlation_results[alarmkey] = {
                            'type': 'derived',
                            'root_name': f"未找到({result['error']})",
                            'path_length': 0,
                            'root_alarmkey': ''
                        }
                elif relationflag == 3:
                    stats['sub_root'] += 1
                    # 查找根源
                    result = self.find_root_alarm(alarm, key_to_alarm)
                    if result['success']:
                        root_name = result['root_alarm'].get('code_name', '未知根源')
                        correlation_results[alarmkey] = {
                            'type': 'sub_root',
                            'root_name': root_name,
                            'path_length': len(result['path']),
                            'root_alarmkey': result['root_alarm'].get('alarm_key', '')
                        }
                    else:
                        correlation_results[alarmkey] = {
                            'type': 'sub_root',
                            'root_name': f"未找到({result['error']})",
                            'path_length': 0,
                            'root_alarmkey': ''  # 修复：补全缺失的字段
                        }
                else:
                    stats['independent'] += 1
                    correlation_results[alarmkey] = {
                        'type': 'independent',
                        'root_name': '',
                        'path_length': 0
                    }

            print(f"关联分析完成:")
            print(f"  根源告警: {stats['root']} 个")
            print(f"  次根源告警: {stats['sub_root']} 个")
            print(f"  衍生告警: {stats['derived']} 个")
            print(f"  独立告警: {stats['independent']} 个")

            return correlation_results

        except Exception as e:
            print(f"关联分析失败: {e}")
            return {}



    def _parse_parent_info(self, raw_data):
        """解析parentinfo字段，提取根源告警的alarmkey"""
        try:
            parentinfo = raw_data.get('parentinfo', {})
            for key, value in parentinfo.items():
                if key.startswith('relation_') and value:
                    # value是数组，取第一个元素
                    relation_str = value[0] if isinstance(value, list) else value
                    # 按@分割，取第一段作为根源alarmkey
                    parts = relation_str.split('@')
                    if parts and parts[0]:
                        print(f"解析到根源alarmkey: {parts[0][:50]}...")
                        return parts[0]  # 根源的alarmkey
        except Exception as e:
            print(f"解析parentinfo失败: {e}")
        return None



    def _extract_extended_fields(self, alarm_data, raw_data):
        """从raw_data中提取完整的203个原始字段"""

        # 辅助函数：安全提取嵌套字段的值
        def safe_extract_value(field_data, default=''):
            if isinstance(field_data, dict):
                return str(field_data.get('value', field_data.get('displayname', default)))
            return str(field_data) if field_data is not None else default

        def safe_extract_nested(field_data, key, default=''):
            if isinstance(field_data, dict):
                return str(field_data.get(key, default))
            return default

        # 基础告警字段组（27-56）
        alarm_data['raw_s_nssai'] = safe_extract_value(raw_data.get('S-NSSAI', {}))
        alarm_data['raw_id'] = str(raw_data.get('id', ''))
        alarm_data['raw_ackinfo'] = str(raw_data.get('ackinfo', ''))
        alarm_data['raw_ackstate'] = str(raw_data.get('ackstate', ''))
        alarm_data['raw_acksystemid'] = str(raw_data.get('acksystemid', ''))
        alarm_data['raw_acktime'] = str(raw_data.get('acktime', ''))
        alarm_data['raw_ackuserid'] = str(raw_data.get('ackuserid', ''))
        alarm_data['raw_admc'] = str(raw_data.get('admc', ''))
        alarm_data['raw_admcname'] = str(raw_data.get('admcname', ''))
        alarm_data['raw_aid'] = str(raw_data.get('aid', ''))
        alarm_data['raw_alarmchangedtime'] = str(raw_data.get('alarmchangedtime', ''))
        alarm_data['raw_alarmcode'] = str(raw_data.get('alarmcode', ''))
        alarm_data['raw_alarmkey'] = str(raw_data.get('alarmkey', ''))
        alarm_data['raw_alarmraisedtime'] = str(raw_data.get('alarmraisedtime', ''))
        alarm_data['raw_alarmsource'] = str(raw_data.get('alarmsource', ''))
        alarm_data['raw_alarmtitle'] = safe_extract_value(raw_data.get('alarmtitle', {}))
        alarm_data['raw_alarmtype'] = str(raw_data.get('alarmtype', ''))
        alarm_data['raw_alarmtypename'] = str(raw_data.get('alarmtypename', ''))
        alarm_data['raw_auxiliarycount'] = str(raw_data.get('auxiliarycount', ''))
        alarm_data['raw_clearstate'] = str(raw_data.get('clearstate', ''))
        alarm_data['raw_clearstatename'] = str(raw_data.get('clearstatename', ''))
        alarm_data['raw_cleartypename'] = str(raw_data.get('cleartypename', ''))
        alarm_data['raw_codename'] = str(raw_data.get('codename', ''))
        alarm_data['raw_commentsystemid'] = str(raw_data.get('commentsystemid', ''))
        alarm_data['raw_commenttext'] = str(raw_data.get('commenttext', ''))
        alarm_data['raw_commenttime'] = str(raw_data.get('commenttime', ''))
        alarm_data['raw_commentuserid'] = str(raw_data.get('commentuserid', ''))
        alarm_data['raw_componentdn'] = str(raw_data.get('componentdn', ''))
        alarm_data['raw_dstsaving'] = str(raw_data.get('dstsaving', ''))
        alarm_data['raw_intermittencecount'] = str(raw_data.get('intermittencecount', ''))

        # 设备和位置字段组（57-86）
        alarm_data['raw_intermittenceduplicatedkey'] = str(raw_data.get('intermittenceduplicatedkey', ''))
        alarm_data['raw_link'] = str(raw_data.get('link', ''))
        alarm_data['raw_linkname'] = str(raw_data.get('linkname', ''))
        alarm_data['raw_maintainstatus'] = safe_extract_value(raw_data.get('maintainstatus', {}))
        alarm_data['raw_me'] = str(raw_data.get('me', ''))
        alarm_data['raw_mename'] = str(raw_data.get('mename', ''))
        alarm_data['raw_moc'] = str(raw_data.get('moc', ''))
        alarm_data['raw_mocname'] = str(raw_data.get('mocname', ''))
        alarm_data['raw_naffiltered'] = str(raw_data.get('naffiltered', ''))
        alarm_data['raw_nbiid'] = str(raw_data.get('nbiid', ''))
        alarm_data['raw_neip'] = str(raw_data.get('neip', ''))

        # 处理数组字段：neplmns
        neplmns = raw_data.get('neplmns', [])
        if isinstance(neplmns, list) and neplmns:
            alarm_data['raw_neplmns'] = ', '.join([f"{item.get('mcc', '')}-{item.get('mnc', '')}" for item in neplmns if isinstance(item, dict)])
        else:
            alarm_data['raw_neplmns'] = str(neplmns) if neplmns else ''

        alarm_data['raw_nmcreasoncode'] = str(raw_data.get('nmcreasoncode', ''))
        alarm_data['raw_offsetalarmraisedtime'] = str(raw_data.get('offsetalarmraisedtime', ''))

        # 处理数组字段：operations
        operations = raw_data.get('operations', [])
        if isinstance(operations, list):
            alarm_data['raw_operations'] = ', '.join([str(op) for op in operations[:3]])  # 只显示前3个
        else:
            alarm_data['raw_operations'] = str(operations) if operations else ''

        # 处理嵌套字段：parentinfo
        parentinfo = raw_data.get('parentinfo', {})
        if isinstance(parentinfo, dict):
            relation_value = parentinfo.get('relation', '')
            alarm_data['raw_parentinfo'] = str(relation_value)
            # 调试：打印parentinfo处理过程
            if parentinfo:  # 只有当parentinfo不为空时才打印
                print(f"🔍 parentinfo处理: 字段={list(parentinfo.keys())} relation='{relation_value}'")
        else:
            alarm_data['raw_parentinfo'] = str(parentinfo) if parentinfo else ''
            if parentinfo:  # 只有当parentinfo不为空时才打印
                print(f"🔍 parentinfo非字典: 类型={type(parentinfo)} 值='{parentinfo}'")

        alarm_data['raw_perceivedseverity'] = str(raw_data.get('perceivedseverity', ''))
        alarm_data['raw_perceivedseverityname'] = str(raw_data.get('perceivedseverityname', ''))
        alarm_data['raw_plmndisplayinfo'] = str(raw_data.get('plmndisplayinfo', ''))

        # 处理数组字段：plmns
        plmns = raw_data.get('plmns', [])
        if isinstance(plmns, list) and plmns:
            alarm_data['raw_plmns'] = ', '.join([f"{item.get('mcc', '')}-{item.get('mnc', '')}" for item in plmns if isinstance(item, dict)])
        else:
            alarm_data['raw_plmns'] = str(plmns) if plmns else ''

        alarm_data['raw_position'] = str(raw_data.get('position', ''))
        alarm_data['raw_positionname'] = str(raw_data.get('positionname', ''))
        alarm_data['raw_productrestype'] = safe_extract_value(raw_data.get('productRestype', {}))
        alarm_data['raw_ranshareswitch'] = str(raw_data.get('ranshareswitch', ''))
        alarm_data['raw_reasoncode'] = str(raw_data.get('reasoncode', ''))
        alarm_data['raw_reasonname'] = str(raw_data.get('reasonname', ''))
        alarm_data['raw_relatedrules'] = str(raw_data.get('relatedrules', ''))
        alarm_data['raw_relatedruletype'] = str(raw_data.get('relatedruletype', ''))
        alarm_data['raw_relationflag'] = str(raw_data.get('relationflag', ''))
        alarm_data['raw_relationflagname'] = str(raw_data.get('relationflagname', ''))

        # RAN专用字段组（87-116）
        alarm_data['raw_relationresult'] = safe_extract_value(raw_data.get('relationresult', {}))
        alarm_data['raw_respath'] = str(raw_data.get('respath', ''))
        alarm_data['raw_restype'] = str(raw_data.get('restype', ''))
        alarm_data['raw_restypename'] = str(raw_data.get('restypename', ''))
        alarm_data['raw_rootcount'] = str(raw_data.get('rootcount', ''))
        alarm_data['raw_sequence'] = str(raw_data.get('sequence', ''))
        alarm_data['raw_servertime'] = str(raw_data.get('servertime', ''))
        alarm_data['raw_timezoneid'] = str(raw_data.get('timezoneid', ''))
        alarm_data['raw_timezoneoffset'] = str(raw_data.get('timezoneoffset', ''))
        alarm_data['raw_visible'] = str(raw_data.get('visible', ''))
        alarm_data['raw_ran_ems_fm_additional_params'] = safe_extract_value(raw_data.get('ran_ems_fm_additional_params', {}))
        alarm_data['raw_ran_fm_alarm_board_type'] = safe_extract_value(raw_data.get('ran_fm_alarm_board_type', {}))
        alarm_data['raw_ran_fm_alarm_dn'] = safe_extract_value(raw_data.get('ran_fm_alarm_dn', {}))
        alarm_data['raw_ran_fm_alarm_location'] = safe_extract_value(raw_data.get('ran_fm_alarm_location', {}))
        alarm_data['raw_ran_fm_alarm_object'] = safe_extract_value(raw_data.get('ran_fm_alarm_object', {}))
        alarm_data['raw_ran_fm_alarm_object_id'] = safe_extract_value(raw_data.get('ran_fm_alarm_object_id', {}))
        alarm_data['raw_ran_fm_alarm_object_name'] = safe_extract_value(raw_data.get('ran_fm_alarm_object_name', {}))
        alarm_data['raw_ran_fm_alarm_object_type'] = safe_extract_value(raw_data.get('ran_fm_alarm_object_type', {}))
        alarm_data['raw_ran_fm_alarm_service_id'] = safe_extract_value(raw_data.get('ran_fm_alarm_service_id', {}))
        alarm_data['raw_ran_fm_alarm_site_name'] = safe_extract_value(raw_data.get('ran_fm_alarm_site_name', {}))
        alarm_data['raw_ran_fm_ne_virtualization'] = safe_extract_value(raw_data.get('ran_fm_ne_virtualization', {}))
        alarm_data['raw_ran_sdr_fm_native_param'] = safe_extract_value(raw_data.get('ran_sdr_fm_native_param', {}))

        # 诊断相关字段组（109-138）
        alarm_data['raw_aax_diagnosisresultstatus'] = safe_extract_value(raw_data.get('aax_DiagnosisResultStatus', {}))
        alarm_data['raw_aax_diagnosisstatus'] = safe_extract_value(raw_data.get('aax_DiagnosisStatus', {}))
        alarm_data['raw_aax_lastdiagnosistime'] = safe_extract_value(raw_data.get('aax_lastDiagnosisTime', {}))
        alarm_data['raw_aax_unrelationflag'] = safe_extract_value(raw_data.get('aax_unrelationflag', {}))

        # 由于字段太多，我们使用通用方法提取所有嵌套字段的详细信息
        self._extract_nested_field_details(alarm_data, raw_data)

    def _extract_nested_field_details(self, alarm_data, raw_data):
        """提取所有嵌套字段的详细信息"""

        def safe_extract_nested(field_data, key, default=''):
            if isinstance(field_data, dict):
                return str(field_data.get(key, default))
            return default

        # 嵌套字段：S-NSSAI详细信息（113-118）
        s_nssai = raw_data.get('S-NSSAI', {})
        alarm_data['raw_s_nssai_columnname'] = safe_extract_nested(s_nssai, 'columnname')
        alarm_data['raw_s_nssai_datatype'] = safe_extract_nested(s_nssai, 'datatype')
        alarm_data['raw_s_nssai_displayname'] = safe_extract_nested(s_nssai, 'displayname')
        alarm_data['raw_s_nssai_extentionfield'] = safe_extract_nested(s_nssai, 'extentionfield')
        alarm_data['raw_s_nssai_value'] = safe_extract_nested(s_nssai, 'value')

        # 嵌套字段：AAX诊断结果状态详细信息（118-123）
        aax_diagnosisresultstatus = raw_data.get('aax_DiagnosisResultStatus', {})
        alarm_data['raw_aax_diagnosisresultstatus_columnname'] = safe_extract_nested(aax_diagnosisresultstatus, 'columnname')
        alarm_data['raw_aax_diagnosisresultstatus_datatype'] = safe_extract_nested(aax_diagnosisresultstatus, 'datatype')
        alarm_data['raw_aax_diagnosisresultstatus_displayname'] = safe_extract_nested(aax_diagnosisresultstatus, 'displayname')
        alarm_data['raw_aax_diagnosisresultstatus_extentionfield'] = safe_extract_nested(aax_diagnosisresultstatus, 'extentionfield')
        alarm_data['raw_aax_diagnosisresultstatus_value'] = safe_extract_nested(aax_diagnosisresultstatus, 'value')

        # 嵌套字段：AAX诊断状态详细信息（123-128）
        aax_diagnosisstatus = raw_data.get('aax_DiagnosisStatus', {})
        alarm_data['raw_aax_diagnosisstatus_columnname'] = safe_extract_nested(aax_diagnosisstatus, 'columnname')
        alarm_data['raw_aax_diagnosisstatus_datatype'] = safe_extract_nested(aax_diagnosisstatus, 'datatype')
        alarm_data['raw_aax_diagnosisstatus_displayname'] = safe_extract_nested(aax_diagnosisstatus, 'displayname')
        alarm_data['raw_aax_diagnosisstatus_extentionfield'] = safe_extract_nested(aax_diagnosisstatus, 'extentionfield')
        alarm_data['raw_aax_diagnosisstatus_value'] = safe_extract_nested(aax_diagnosisstatus, 'value')

        # 嵌套字段：AAX最后诊断时间详细信息（128-133）
        aax_lastdiagnosistime = raw_data.get('aax_lastDiagnosisTime', {})
        alarm_data['raw_aax_lastdiagnosistime_columnname'] = safe_extract_nested(aax_lastdiagnosistime, 'columnname')
        alarm_data['raw_aax_lastdiagnosistime_datatype'] = safe_extract_nested(aax_lastdiagnosistime, 'datatype')
        alarm_data['raw_aax_lastdiagnosistime_displayname'] = safe_extract_nested(aax_lastdiagnosistime, 'displayname')
        alarm_data['raw_aax_lastdiagnosistime_extentionfield'] = safe_extract_nested(aax_lastdiagnosistime, 'extentionfield')
        alarm_data['raw_aax_lastdiagnosistime_value'] = safe_extract_nested(aax_lastdiagnosistime, 'value')

        # 嵌套字段：AAX非关联标志详细信息（133-138）
        aax_unrelationflag = raw_data.get('aax_unrelationflag', {})
        alarm_data['raw_aax_unrelationflag_columnname'] = safe_extract_nested(aax_unrelationflag, 'columnname')
        alarm_data['raw_aax_unrelationflag_datatype'] = safe_extract_nested(aax_unrelationflag, 'datatype')
        alarm_data['raw_aax_unrelationflag_displayname'] = safe_extract_nested(aax_unrelationflag, 'displayname')
        alarm_data['raw_aax_unrelationflag_extentionfield'] = safe_extract_nested(aax_unrelationflag, 'extentionfield')
        alarm_data['raw_aax_unrelationflag_value'] = safe_extract_nested(aax_unrelationflag, 'value')

        # 嵌套字段：告警标题详细信息（138-143）
        alarmtitle = raw_data.get('alarmtitle', {})
        alarm_data['raw_alarmtitle_columnname'] = safe_extract_nested(alarmtitle, 'columnname')
        alarm_data['raw_alarmtitle_datatype'] = safe_extract_nested(alarmtitle, 'datatype')
        alarm_data['raw_alarmtitle_displayname'] = safe_extract_nested(alarmtitle, 'displayname')
        alarm_data['raw_alarmtitle_extentionfield'] = safe_extract_nested(alarmtitle, 'extentionfield')
        alarm_data['raw_alarmtitle_value'] = safe_extract_nested(alarmtitle, 'value')

        # 嵌套字段：维护状态详细信息（143-148）
        maintainstatus = raw_data.get('maintainstatus', {})
        alarm_data['raw_maintainstatus_columnname'] = safe_extract_nested(maintainstatus, 'columnname')
        alarm_data['raw_maintainstatus_datatype'] = safe_extract_nested(maintainstatus, 'datatype')
        alarm_data['raw_maintainstatus_displayname'] = safe_extract_nested(maintainstatus, 'displayname')
        alarm_data['raw_maintainstatus_extentionfield'] = safe_extract_nested(maintainstatus, 'extentionfield')
        alarm_data['raw_maintainstatus_value'] = safe_extract_nested(maintainstatus, 'value')

        # 嵌套字段：网元PLMN详细信息（148-150）
        neplmns = raw_data.get('neplmns', [])
        if isinstance(neplmns, list) and neplmns:
            first_neplmn = neplmns[0] if isinstance(neplmns[0], dict) else {}
            alarm_data['raw_neplmns_mcc'] = str(first_neplmn.get('mcc', ''))
            alarm_data['raw_neplmns_mnc'] = str(first_neplmn.get('mnc', ''))
        else:
            alarm_data['raw_neplmns_mcc'] = ''
            alarm_data['raw_neplmns_mnc'] = ''

        # 嵌套字段：父级信息详细信息（150-155）
        parentinfo = raw_data.get('parentinfo', {})
        relation_value = safe_extract_nested(parentinfo, 'relation')
        alarm_data['raw_parentinfo_relation'] = relation_value
        alarm_data['raw_parentinfo_relation_2025_04'] = safe_extract_nested(parentinfo, 'relation_2025_04')
        alarm_data['raw_parentinfo_relation_2025_06'] = safe_extract_nested(parentinfo, 'relation_2025_06')
        alarm_data['raw_parentinfo_relation_2025_07'] = safe_extract_nested(parentinfo, 'relation_2025_07')
        alarm_data['raw_parentinfo_relation_2025_08'] = safe_extract_nested(parentinfo, 'relation_2025_08')

        # 调试：打印嵌套字段提取结果
        if parentinfo and isinstance(parentinfo, dict):
            print(f"🔍 嵌套parentinfo: 字段={list(parentinfo.keys())} relation='{relation_value}'")

        # 嵌套字段：PLMN列表详细信息（155-157）
        plmns = raw_data.get('plmns', [])
        if isinstance(plmns, list) and plmns:
            first_plmn = plmns[0] if isinstance(plmns[0], dict) else {}
            alarm_data['raw_plmns_mcc'] = str(first_plmn.get('mcc', ''))
            alarm_data['raw_plmns_mnc'] = str(first_plmn.get('mnc', ''))
        else:
            alarm_data['raw_plmns_mcc'] = ''
            alarm_data['raw_plmns_mnc'] = ''

        # 继续提取其他嵌套字段...
        self._extract_remaining_nested_fields(alarm_data, raw_data)

    def _extract_remaining_nested_fields(self, alarm_data, raw_data):
        """提取剩余的嵌套字段详细信息"""

        def safe_extract_nested(field_data, key, default=''):
            if isinstance(field_data, dict):
                return str(field_data.get(key, default))
            return default

        # 嵌套字段：产品资源类型详细信息（157-162）
        productrestype = raw_data.get('productRestype', {})
        alarm_data['raw_productrestype_columnname'] = safe_extract_nested(productrestype, 'columnname')
        alarm_data['raw_productrestype_datatype'] = safe_extract_nested(productrestype, 'datatype')
        alarm_data['raw_productrestype_displayname'] = safe_extract_nested(productrestype, 'displayname')
        alarm_data['raw_productrestype_extentionfield'] = safe_extract_nested(productrestype, 'extentionfield')
        alarm_data['raw_productrestype_value'] = safe_extract_nested(productrestype, 'value')

        # 嵌套字段：RAN EMS参数详细信息（162-167）
        ran_ems_params = raw_data.get('ran_ems_fm_additional_params', {})
        alarm_data['raw_ran_ems_params_columnname'] = safe_extract_nested(ran_ems_params, 'columnname')
        alarm_data['raw_ran_ems_params_datatype'] = safe_extract_nested(ran_ems_params, 'datatype')
        alarm_data['raw_ran_ems_params_displayname'] = safe_extract_nested(ran_ems_params, 'displayname')
        alarm_data['raw_ran_ems_params_extentionfield'] = safe_extract_nested(ran_ems_params, 'extentionfield')
        alarm_data['raw_ran_ems_params_value'] = safe_extract_nested(ran_ems_params, 'value')

        # 嵌套字段：RAN告警板类型详细信息（167-172）
        ran_board_type = raw_data.get('ran_fm_alarm_board_type', {})
        alarm_data['raw_ran_board_type_columnname'] = safe_extract_nested(ran_board_type, 'columnname')
        alarm_data['raw_ran_board_type_datatype'] = safe_extract_nested(ran_board_type, 'datatype')
        alarm_data['raw_ran_board_type_displayname'] = safe_extract_nested(ran_board_type, 'displayname')
        alarm_data['raw_ran_board_type_extentionfield'] = safe_extract_nested(ran_board_type, 'extentionfield')
        alarm_data['raw_ran_board_type_value'] = safe_extract_nested(ran_board_type, 'value')

        # 嵌套字段：RAN告警DN详细信息（172-177）
        ran_alarm_dn = raw_data.get('ran_fm_alarm_dn', {})
        alarm_data['raw_ran_alarm_dn_columnname'] = safe_extract_nested(ran_alarm_dn, 'columnname')
        alarm_data['raw_ran_alarm_dn_datatype'] = safe_extract_nested(ran_alarm_dn, 'datatype')
        alarm_data['raw_ran_alarm_dn_displayname'] = safe_extract_nested(ran_alarm_dn, 'displayname')
        alarm_data['raw_ran_alarm_dn_extentionfield'] = safe_extract_nested(ran_alarm_dn, 'extentionfield')
        alarm_data['raw_ran_alarm_dn_value'] = safe_extract_nested(ran_alarm_dn, 'value')

        # 嵌套字段：RAN告警位置详细信息（177-182）
        ran_alarm_location = raw_data.get('ran_fm_alarm_location', {})
        alarm_data['raw_ran_alarm_location_columnname'] = safe_extract_nested(ran_alarm_location, 'columnname')
        alarm_data['raw_ran_alarm_location_datatype'] = safe_extract_nested(ran_alarm_location, 'datatype')
        alarm_data['raw_ran_alarm_location_displayname'] = safe_extract_nested(ran_alarm_location, 'displayname')
        alarm_data['raw_ran_alarm_location_extentionfield'] = safe_extract_nested(ran_alarm_location, 'extentionfield')
        alarm_data['raw_ran_alarm_location_value'] = safe_extract_nested(ran_alarm_location, 'value')

        # 嵌套字段：RAN告警对象详细信息（182-187）
        ran_alarm_object = raw_data.get('ran_fm_alarm_object', {})
        alarm_data['raw_ran_alarm_object_columnname'] = safe_extract_nested(ran_alarm_object, 'columnname')
        alarm_data['raw_ran_alarm_object_datatype'] = safe_extract_nested(ran_alarm_object, 'datatype')
        alarm_data['raw_ran_alarm_object_displayname'] = safe_extract_nested(ran_alarm_object, 'displayname')
        alarm_data['raw_ran_alarm_object_extentionfield'] = safe_extract_nested(ran_alarm_object, 'extentionfield')
        alarm_data['raw_ran_alarm_object_value'] = safe_extract_nested(ran_alarm_object, 'value')

        # 嵌套字段：RAN告警对象ID详细信息（187-192）
        ran_alarm_object_id = raw_data.get('ran_fm_alarm_object_id', {})
        alarm_data['raw_ran_alarm_object_id_columnname'] = safe_extract_nested(ran_alarm_object_id, 'columnname')
        alarm_data['raw_ran_alarm_object_id_datatype'] = safe_extract_nested(ran_alarm_object_id, 'datatype')
        alarm_data['raw_ran_alarm_object_id_displayname'] = safe_extract_nested(ran_alarm_object_id, 'displayname')
        alarm_data['raw_ran_alarm_object_id_extentionfield'] = safe_extract_nested(ran_alarm_object_id, 'extentionfield')
        alarm_data['raw_ran_alarm_object_id_value'] = safe_extract_nested(ran_alarm_object_id, 'value')

        # 嵌套字段：RAN告警对象名详细信息（192-197）
        ran_alarm_object_name = raw_data.get('ran_fm_alarm_object_name', {})
        alarm_data['raw_ran_alarm_object_name_columnname'] = safe_extract_nested(ran_alarm_object_name, 'columnname')
        alarm_data['raw_ran_alarm_object_name_datatype'] = safe_extract_nested(ran_alarm_object_name, 'datatype')
        alarm_data['raw_ran_alarm_object_name_displayname'] = safe_extract_nested(ran_alarm_object_name, 'displayname')
        alarm_data['raw_ran_alarm_object_name_extentionfield'] = safe_extract_nested(ran_alarm_object_name, 'extentionfield')
        alarm_data['raw_ran_alarm_object_name_value'] = safe_extract_nested(ran_alarm_object_name, 'value')

        # 嵌套字段：RAN告警对象类型详细信息（197-202）
        ran_alarm_object_type = raw_data.get('ran_fm_alarm_object_type', {})
        alarm_data['raw_ran_alarm_object_type_columnname'] = safe_extract_nested(ran_alarm_object_type, 'columnname')
        alarm_data['raw_ran_alarm_object_type_datatype'] = safe_extract_nested(ran_alarm_object_type, 'datatype')
        alarm_data['raw_ran_alarm_object_type_displayname'] = safe_extract_nested(ran_alarm_object_type, 'displayname')
        alarm_data['raw_ran_alarm_object_type_extentionfield'] = safe_extract_nested(ran_alarm_object_type, 'extentionfield')
        alarm_data['raw_ran_alarm_object_type_value'] = safe_extract_nested(ran_alarm_object_type, 'value')

        # 最后的字段（202-203）
        relatedruletype = raw_data.get('relatedruletype', {})
        alarm_data['raw_relatedruletype_null'] = safe_extract_nested(relatedruletype, 'null')

        relationresult = raw_data.get('relationresult', {})
        alarm_data['raw_relationresult_value'] = safe_extract_nested(relationresult, 'value')



    def stop(self):
        """请求停止线程"""
        self._stop_requested = True

    def run(self):
        try:
            if self._stop_requested:
                return

            self.progress_update.emit(10)
            self.log_message.emit("开始加载告警数据...")
            alarms = self.get_alarm_data()

            if self._stop_requested:
                return

            self.progress_update.emit(70)
            self.log_message.emit("开始加载统计信息...")
            stats = self.get_statistics()

            if self._stop_requested:
                return

            print(f"数据处理完成，最终告警数量: {len(alarms)}")



            self.progress_update.emit(100)
            self.log_message.emit(f"数据加载完成 - 告警:{len(alarms)}条")
            self.finished_ok.emit(alarms, stats)

        except Exception as e:
            import logging
            logging.exception("数据加载异常")  # 记录完整异常信息
            error_msg = f"数据加载错误: {e}"
            self.log_message.emit(error_msg)
            self.finished_err.emit(error_msg)

    def get_alarm_data(self):
        """获取告警数据"""
        try:
            with sqlite3.connect(self.db_file, timeout=5.0) as conn:
                # 启用WAL模式，提高并发性能
                conn.execute("PRAGMA journal_mode=WAL")
                cursor = conn.cursor()

                # 检查数据库表结构
                cursor.execute("PRAGMA table_info(alarms)")
                table_info = cursor.fetchall()
                columns = [col[1] for col in table_info]
                print(f"数据库表实际字段数量: {len(columns)}")
                print(f"数据库表字段: {columns}")

                # 详细显示字段信息
                print("字段详细信息:")
                for col in table_info:
                    print(f"  {col[1]} ({col[2]}) - 非空:{col[3]} - 默认值:{col[4]}")

                has_effective_duration = 'effective_duration_minutes' in columns

                # 检查is_new字段的分布情况
                try:
                    cursor.execute("SELECT is_new, COUNT(*) FROM alarms WHERE is_active = 1 GROUP BY is_new")
                    is_new_stats = cursor.fetchall()
                    print(f"is_new字段分布: {dict(is_new_stats)}")
                except Exception as e:
                    print(f"检查is_new字段失败: {e}")



                # 检查is_baseline字段是否存在
                has_baseline = 'is_baseline' in columns

                # 检查actual_duration_minutes字段是否存在
                has_actual_duration = 'actual_duration_minutes' in columns

                # 构建完整的查询语句，包含所有27个字段
                base_fields = """
                    id, code_name, perceived_severity_name, me_name, ne_ip,
                    alarm_raised_time,
                    {duration_field},
                    {baseline_field},
                    ack_state_name, alarm_type_name, reason_name, additional_text,
                    position_name, alarm_code, res_type_name, clear_state_name,
                    ack_user_id, comment_text, first_seen_at, last_seen_at,
                    alarm_key, is_new, is_active, status_changed_at, created_at,
                    {actual_duration_field},
                    raw_data
                """.format(
                    baseline_field="is_baseline" if has_baseline else "0 as is_baseline",
                    duration_field="effective_duration_minutes" if has_effective_duration else "0 as effective_duration_minutes",
                    actual_duration_field="actual_duration_minutes" if has_actual_duration else "0 as actual_duration_minutes"
                )

                base_query = f"""
                    SELECT {base_fields}
                    FROM alarms
                    WHERE is_active = 1
                """

                # 筛选逻辑已删除，显示所有告警

                base_query += " ORDER BY alarm_raised_time DESC"  # 显示所有数据，不限制数量

                print(f"执行查询: {base_query}")
                cursor.execute(base_query)
                results = cursor.fetchall()
                print(f"查询结果数量: {len(results)}")

                # 收集所有告警数据
                try:
                    raw_alarms_data = []
                    for row in results:
                        # 注意：raw_data 在SELECT中的索引为26（最后一列）
                        raw_data_str = row[26] if len(row) > 26 else '{}'
                        try:
                            raw_data = json.loads(raw_data_str) if raw_data_str else {}
                            alarmkey = raw_data.get('alarmkey', '')
                        except Exception:
                            raw_data = {}
                            alarmkey = ''

                        raw_alarms_data.append({
                            'id': row[0],
                            'alarmkey': alarmkey,
                            'code_name': row[1],
                            'raw_data': raw_data,
                            'alarm_key': str(row[20]) if len(row) > 20 and row[20] else ''  # 添加数据库alarm_key字段
                        })

                    # 分析告警关联关系（添加进度提示）
                    self.log_message.emit(f"🔗 开始分析 {len(raw_alarms_data)} 条告警的关联关系...")
                    correlation_results = self.analyze_alarm_correlations(raw_alarms_data)
                    self.log_message.emit("🔗 告警关联分析完成")

                except Exception as e:
                    print(f"数据收集失败: {e}")
                    correlation_results = {}

                alarms = []
                processed_count = 0  # 用于调试打印计数
                print(f"开始处理 {len(results)} 条告警数据")
                if len(results) > 0:
                    print(f"第一行数据长度: {len(results[0])}")
                    print(f"第一行数据前5个字段: {results[0][:5]}")

                for row_idx, row in enumerate(results):
                    if self._stop_requested:  # 检查停止请求
                        break

                    if row_idx % 1000 == 0:
                        print(f"已处理 {row_idx} 条告警")

                    # 每处理100行检查一次停止请求
                    if row_idx % 100 == 0 and self._stop_requested:
                        break

                    try:
                        # 新的字段索引（完整27字段版）：
                        # 0:id, 1:code_name, 2:perceived_severity_name, 3:me_name, 4:ne_ip,
                        # 5:alarm_raised_time, 6:effective_duration_minutes, 7:is_baseline,
                        # 8:ack_state_name, 9:alarm_type_name, 10:reason_name, 11:additional_text,
                        # 12:position_name, 13:alarm_code, 14:res_type_name, 15:clear_state_name,
                        # 16:ack_user_id, 17:comment_text, 18:first_seen_at, 19:last_seen_at,
                        # 20:alarm_key, 21:is_new, 22:is_active, 23:status_changed_at, 24:created_at,
                        # 25:actual_duration_minutes, 26:raw_data

                        raw_data = json.loads(row[26]) if len(row) > 26 and row[26] else {}
                        relationflag = raw_data.get('relationflag', 0)
                        rootcount = raw_data.get('rootcount', 0)

                        # 调试：检查前3条记录的parentinfo
                        if row_idx < 3:
                            print(f"🔍 数据库记录{row_idx+1}:")
                            print(f"    relationflag: {relationflag}")
                            if 'parentinfo' in raw_data:
                                parentinfo = raw_data['parentinfo']
                                print(f"    parentinfo类型: {type(parentinfo)}")
                                if isinstance(parentinfo, dict):
                                    print(f"    parentinfo字段: {list(parentinfo.keys())}")
                                    if 'relation' in parentinfo:
                                        print(f"    parentinfo.relation: '{parentinfo['relation']}'")
                                else:
                                    print(f"    parentinfo值: {parentinfo}")
                            else:
                                print(f"    无parentinfo字段")

                        # 获取effective_duration_minutes字段
                        effective_duration_minutes = row[6] if len(row) > 6 and row[6] else 0

                        # 生成状态标识 - 完全对齐web.py逻辑
                        status_marks = []
                        is_new = row[21] if len(row) > 21 and row[21] is not None else 0
                        is_baseline = row[7] if len(row) > 7 and row[7] is not None else 0
                        is_active = row[22] if len(row) > 22 and row[22] is not None else 1

                        # 完全对齐web.py的状态判断逻辑
                        if is_baseline == 1 and is_active == 1:
                            status_marks.append("📊基线")
                        elif is_new == 1 and is_active == 1:
                            status_marks.append("🆕新的")
                        elif is_new == 0 and is_active == 1:
                            status_marks.append("📍持续")
                        else:
                            status_marks.append("❌已清除")

                        # 生成关联标记 - 使用关联分析结果
                        relation_marks = []
                        current_alarmkey = row[20] if len(row) > 20 else ''  # alarm_key字段在第20位
                        correlation_info = correlation_results.get(current_alarmkey, {})

                        if relationflag == 1:
                            relation_marks.append("🔴根源")
                            if rootcount > 0:
                                relation_marks.append(f"(影响{rootcount}个)")
                        elif relationflag == 2:
                            root_name = correlation_info.get('root_name', '')
                            if root_name and not root_name.startswith('未找到'):
                                # 截断过长的根源名称
                                short_root_name = root_name[:8] + '...' if len(root_name) > 8 else root_name
                                relation_marks.append(f"🟡衍生←{short_root_name}")
                            else:
                                relation_marks.append("🟡衍生")
                        elif relationflag == 3:
                            root_name = correlation_info.get('root_name', '')
                            if root_name and not root_name.startswith('未找到'):
                                # 截断过长的根源名称
                                short_root_name = root_name[:8] + '...' if len(root_name) > 8 else root_name
                                relation_marks.append(f"🟠次根源←{short_root_name}")
                            else:
                                relation_marks.append("🟠次根源")

                        # 调试：显示样本（只打印前20条衍生/次根源）
                        if relationflag in (2, 3) and processed_count < 20:
                            print(f"  [显示样本{processed_count+1}] rf={relationflag} key={current_alarmkey[:18]}... "
                                  f"root_name='{correlation_info.get('root_name', '')}' "
                                  f"marks='{' '.join(relation_marks)}'")
                            processed_count += 1

                        # 检查重点关注 - 单独处理，不与关联标记混合
                        focus_marks = []
                        if any(keyword in str(row[1]) for keyword in self.focus_keywords):
                            focus_marks.append("🎯重点")

                        # 判断运营商 - 根据PLMN显示信息
                        operator_info = self.determine_operator(raw_data.get('plmndisplayinfo', ''))

                        # 根源ID（用于分组/显示）：根源=自身alarm_key；衍生/次根源=追根的root_alarmkey
                        root_group_id = ''
                        if relationflag == 1:
                            root_group_id = str(row[20]) if len(row) > 20 else ''
                        else:
                            root_group_id = correlation_info.get('root_alarmkey', '')

                        # 合并所有标记：关联标记 + 重点标记
                        all_marks = relation_marks + focus_marks

                        # 格式化时间
                        if row[5]:
                            try:
                                alarm_time = datetime.fromtimestamp(row[5] / 1000)
                                time_str = alarm_time.strftime("%m-%d %H:%M")
                            except:
                                time_str = "时间错误"
                        else:
                            time_str = "未知"

                        # 使用考核持续时间
                        if effective_duration_minutes and effective_duration_minutes > 0:
                            # 使用数据库中的考核持续时间
                            duration_str = format_duration_text(effective_duration_minutes)
                        elif row[5]:
                            # 如果数据库中没有，实时计算考核持续时间
                            try:
                                alarm_time = datetime.fromtimestamp(row[5] / 1000)
                                now = datetime.now()
                                effective_minutes = calculate_effective_duration(alarm_time, now)
                                duration_str = format_duration_text(effective_minutes)
                            except:
                                duration_str = "计算错误"
                        else:
                            duration_str = "未知"

                        # 构建完整的告警数据，包含所有字段
                        alarm_data = {
                            # 基础字段（0-28）
                            'id': row[0],
                            'status_marks': " ".join(status_marks),
                            'relation_marks': " ".join(all_marks),
                            'focus_marks': " ".join(focus_marks),
                            'operator_info': operator_info,
                                'root_group_id_short': (root_group_id[:8] + '...') if root_group_id and len(root_group_id) > 8 else root_group_id,
                            'code_name': str(row[1])[:MAX_CODE_NAME_LENGTH] if row[1] else "未知",
                            'severity': str(row[2])[:20] if row[2] else "未知",
                            'me_name': str(row[3])[:MAX_ME_NAME_LENGTH] if row[3] else "未知",
                            'ne_ip': str(row[4])[:MAX_IP_LENGTH] if row[4] else "未知",
                            'time_str': time_str,
                            'duration_str': duration_str,
                            'effective_duration_minutes': effective_duration_minutes,
                            'relationflag': relationflag,
                            'is_new': row[21] if len(row) > 21 else 0,
                            'raw_data': raw_data,
                            'ack_state_name': str(row[8]) if len(row) > 8 and row[8] else "未知",
                            'alarm_type_name': str(row[9]) if len(row) > 9 and row[9] else "未知",
                            'reason_name': str(row[10]) if len(row) > 10 and row[10] else "未知",
                            'additional_text': str(row[11]) if len(row) > 11 and row[11] else "未知",
                            'position_name': str(row[12]) if len(row) > 12 and row[12] else "未知",
                            'alarm_code': str(row[13]) if len(row) > 13 and row[13] else "未知",
                            'res_type_name': str(row[14]) if len(row) > 14 and row[14] else "未知",
                            'clear_state_name': str(row[15]) if len(row) > 15 and row[15] else "未知",
                            'ack_user_id': str(row[16]) if len(row) > 16 and row[16] else "未知",
                            'comment_text': str(row[17]) if len(row) > 17 and row[17] else "未知",
                            'first_seen_at': str(row[18]) if len(row) > 18 and row[18] else "未知",
                            'last_seen_at': str(row[19]) if len(row) > 19 and row[19] else "未知",
                            'alarm_key': str(row[20]) if len(row) > 20 and row[20] else "未知",
                            'is_active': row[22] if len(row) > 22 else 1,
                            'status_changed_at': str(row[23]) if len(row) > 23 and row[23] else "未知",
                            'created_at': str(row[24]) if len(row) > 24 and row[24] else "未知",
                            'actual_duration_minutes': row[25] if len(row) > 25 and row[25] else 0,
                            'is_baseline': row[7] if len(row) > 7 else 0,
                                'root_group_id': root_group_id,  # 根源分组ID（用于分组/显示/导出）
                        }

                        # 从raw_data中提取扩展字段（27-61）
                        self._extract_extended_fields(alarm_data, raw_data)





                        alarms.append(alarm_data)
                    except Exception as e:
                        print(f"处理第{row_idx}条告警数据时出错: {e}")
                        print(f"   行数据长度: {len(row)}")
                        if len(row) > 8:
                            print(f"   raw_data前100字符: {str(row[8])[:100]}")
                        import traceback
                        print(f"   异常详情: {traceback.format_exc()}")
                        continue  # 跳过有问题的行

                return alarms

        except Exception as e:
            print(f"获取告警数据失败: {e}")
            return []
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

    def get_statistics(self):
        """获取统计信息 - 优化版本（保持100%兼容）"""
        conn = None
        try:
            # 增加数据库连接超时时间（安全优化）
            conn = sqlite3.connect(self.db_file, timeout=30.0)
            cursor = conn.cursor()

            # 设置更长的查询超时时间（安全优化）
            cursor.execute("PRAGMA busy_timeout = 30000")  # 30秒超时

            # 优化查询计划（安全优化，不改变结果）
            cursor.execute("ANALYZE")

            if self._stop_requested:
                return {'total': 0, 'root': 0, 'derived': 0, 'independent': 0, 'focus': 0}

            # 添加进度提示（用户体验优化）
            self.log_message.emit("📊 正在统计活跃告警总数...")
            cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1")
            total = cursor.fetchone()[0]

            if self._stop_requested:
                return {'total': 0, 'new': 0, 'baseline': 0, 'continuing': 0, 'root': 0, 'derived': 0, 'independent': 0, 'focus': 0}

            # 状态统计 - 完全对齐web.py逻辑（添加进度提示）
            self.log_message.emit("📊 正在统计新告警数量...")
            cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND is_new = 1")
            new_count = cursor.fetchone()[0]

            self.log_message.emit("📊 正在统计基线告警数量...")
            cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND is_baseline = 1")
            baseline_count = cursor.fetchone()[0]

            self.log_message.emit("📊 正在统计持续告警数量...")
            cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND is_new = 0 AND (is_baseline = 0 OR is_baseline IS NULL)")
            continuing_count = cursor.fetchone()[0]

            # 优化：使用预处理的字段而不是json_extract，避免卡死
            self.log_message.emit("📊 正在统计根源告警数量...")
            try:
                cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND raw_relationflag = '1'")
                root_count = cursor.fetchone()[0]
            except:
                # 降级到json_extract，但设置超时（保持100%兼容性）
                self.log_message.emit("📊 使用兼容模式统计根源告警...")
                cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND json_extract(raw_data, '$.relationflag') = 1")
                root_count = cursor.fetchone()[0]

            self.log_message.emit("📊 正在统计衍生告警数量...")
            try:
                cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND raw_relationflag = '2'")
                derived_count = cursor.fetchone()[0]
            except:
                # 降级到json_extract，但设置超时（保持100%兼容性）
                self.log_message.emit("📊 使用兼容模式统计衍生告警...")
                cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1 AND json_extract(raw_data, '$.relationflag') = 2")
                derived_count = cursor.fetchone()[0]

            independent_count = total - root_count - derived_count

            # 优化：使用更简单的查询（保持原有逻辑不变）
            self.log_message.emit("📊 正在统计重点告警数量...")
            cursor.execute("""
                SELECT COUNT(*) FROM alarms
                WHERE is_active = 1 AND (
                    code_name LIKE '%小区退%' OR
                    code_name LIKE '%天馈驻波比异常%'
                )
            """)
            focus_count = cursor.fetchone()[0]

            self.log_message.emit("📊 统计信息计算完成")

            return {
                'total': total,
                'new': new_count,
                'baseline': baseline_count,
                'continuing': continuing_count,
                'root': root_count,
                'derived': derived_count,
                'independent': independent_count,
                'focus': focus_count
            }
        except Exception as e:
            print(f"获取统计失败: {e}")
            return {'total': 0, 'new': 0, 'baseline': 0, 'continuing': 0, 'root': 0, 'derived': 0, 'independent': 0, 'focus': 0}
        finally:
            if conn:
                try:
                    conn.close()
                except:
                    pass

class AlarmDetailDialog(QDialog):
    """告警详情对话框"""

    def __init__(self, alarm_data, parent=None):
        super().__init__(parent)
        self.alarm_data = alarm_data
        self.setWindowTitle(f"告警详情 - {alarm_data['code_name']}")
        self.setModal(True)
        self.resize(800, 600)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 基本信息
        info_group = QGroupBox("📍 基本信息")
        info_layout = QVBoxLayout(info_group)

        # 解析关联信息
        relation_info = ""
        raw_data = self.alarm_data.get('raw_data', {})
        if isinstance(raw_data, str):
            try:
                raw_data = json.loads(raw_data)
            except:
                raw_data = {}

        relationflag = raw_data.get('relationflag', 0)
        if relationflag == 1:
            rootcount = raw_data.get('rootcount', 0)
            relation_info = f"根源告警，影响{rootcount}个衍生告警"
        elif relationflag == 2:
            # 尝试解析parentinfo
            parentinfo = raw_data.get('parentinfo', {})
            root_alarmkey = None
            for key, value in parentinfo.items():
                if key.startswith('relation_') and value:
                    relation_str = value[0] if isinstance(value, list) else value
                    parts = relation_str.split('@')
                    if parts and parts[0]:
                        root_alarmkey = parts[0]
                        break

            if root_alarmkey:
                relation_info = f"衍生告警，根源Key: {root_alarmkey[:20]}..."
            else:
                relation_info = "衍生告警"
        else:
            relation_info = "独立告警"

        basic_info = f"""告警名称: {self.alarm_data['code_name']}
告警级别: {self.alarm_data['severity']}
网元名称: {self.alarm_data['me_name']}
IP地址: {self.alarm_data['ne_ip']}
发生时间: {self.alarm_data['time_str']}
考核持续时间: {self.alarm_data['duration_str']}
状态标记: {self.alarm_data.get('status_marks', '')}
关联标记: {self.alarm_data.get('relation_marks', '')}
关联信息: {relation_info}
考核说明: 排除每天0:00-5:59非考核时间段"""

        info_label = QLabel(basic_info)
        info_label.setFont(QFont("Consolas", 10))
        info_layout.addWidget(info_label)

        # 原始数据
        raw_group = QGroupBox("🔧 原始数据")
        raw_layout = QVBoxLayout(raw_group)

        raw_text = QTextEdit()
        raw_text.setFont(QFont("Consolas", 9))
        raw_text.setPlainText(json.dumps(self.alarm_data['raw_data'], indent=2, ensure_ascii=False))
        raw_text.setReadOnly(True)
        raw_layout.addWidget(raw_text)

        # 按钮
        button_layout = QHBoxLayout()
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addWidget(info_group)
        layout.addWidget(raw_group)
        layout.addLayout(button_layout)

class AlarmMonitorGUI(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.db_file = DB_FILE
        self.alarms_data = []
        self.last_refresh_time = datetime.now()
        self.refresh_count = 0
        self.new_alarms_count = 0
        self.start_time = datetime.now()  # 程序启动时间
        self.sent_email_keys = set()  # 本次运行已发送的告警键，避免重复邮件

        # 🚀 智能分页相关
        self.all_alarms = []  # 完整告警数据
        self.displayed_alarms = []  # 当前显示的告警数据
        self.current_display_count = 1000  # 当前显示数量
        self.priority_count = 0  # 优先告警数量
        self.smart_mode = True  # 智能排序模式（True=智能排序，False=时间排序）

        # 📧 邮件管理器将在UI初始化完成后创建
        self.email_manager = None

        # 定时获取定时器
        self.fetch_timer = QTimer()
        self.fetch_timer.timeout.connect(self.auto_fetch_from_web)
        self.fetch_timer_enabled = False
        self.fetch_interval_minutes = 10
        self.fetch_worker = None

        # 加载重点关键字配置
        self.focus_keywords = self.load_focus_keywords()



        self.setWindowTitle("🚨 告警监控系统 - PySide6版本 (优化版)")
        self.setGeometry(100, 100, WINDOW_WIDTH, WINDOW_HEIGHT)

        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                color: black;
            }
            QTableWidget::item {
                color: black;
                padding: 4px;
            }
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
            QHeaderView::section {
                background-color: #e0e0e0;
                color: black;
                padding: 6px;
                border: 1px solid #c0c0c0;
                font-weight: bold;
            }
        """)

        self.setup_ui()
        self.setup_timer()

        # 📧 在UI初始化完成后创建邮件管理器
        try:
            self.email_manager = EmailManager(CONFIG_FILE, self)
            self.add_log("📧 邮件系统初始化完成")
        except Exception as e:
            self.add_log(f"❌ 邮件系统初始化失败: {e}")
            # 创建一个默认的邮件管理器
            self.email_manager = EmailManager(CONFIG_FILE, None)

        # 添加启动日志
        self.add_log("🚀 告警监控系统启动 (优化版)")
        self.add_log(f"🎯 重点关注关键字: {', '.join(self.focus_keywords)}")

        self.refresh_data()

    def load_focus_keywords(self):
        """从配置文件加载重点关键字"""
        try:
            config = configparser.ConfigParser()
            config.read(CONFIG_FILE, encoding='utf-8')
            keywords_str = config.get('focus', 'keywords', fallback='\n'.join(DEFAULT_FOCUS_KEYWORDS))
            # 按行分割，去除空行和空白
            keywords = [kw.strip() for kw in keywords_str.split('\n') if kw.strip()]
            return keywords if keywords else DEFAULT_FOCUS_KEYWORDS
        except:
            return DEFAULT_FOCUS_KEYWORDS

    def save_column_widths(self):
        """保存列宽设置"""
        try:
            config = configparser.ConfigParser()
            config.read(CONFIG_FILE, encoding='utf-8')

            # 确保有column_widths节
            if not config.has_section('column_widths'):
                config.add_section('column_widths')

            # 保存每列的宽度
            for i in range(self.table.columnCount()):
                width = self.table.columnWidth(i)
                config.set('column_widths', f'column_{i}', str(width))

            # 写入配置文件
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)
        except Exception as e:
            print(f"保存列宽失败: {e}")

    def restore_column_widths(self):
        """恢复列宽设置"""
        try:
            config = configparser.ConfigParser()
            config.read(CONFIG_FILE, encoding='utf-8')

            if config.has_section('column_widths'):
                for i in range(self.table.columnCount()):
                    width_key = f'column_{i}'
                    if config.has_option('column_widths', width_key):
                        width = config.getint('column_widths', width_key)
                        # 忽略宽度为0的设置，避免隐藏列
                        if width > 0:
                            self.table.setColumnWidth(i, width)
        except Exception as e:
            print(f"恢复列宽失败: {e}")

    def show_table_context_menu(self, position):
        """显示表格右键菜单"""
        from PySide6.QtWidgets import QMenu, QAction

        menu = QMenu(self)

        # 列宽调整选项
        auto_resize_action = QAction("🔧 自动调整列宽", self)
        auto_resize_action.triggered.connect(self.auto_resize_columns)
        menu.addAction(auto_resize_action)

        reset_width_action = QAction("↩️ 重置列宽", self)
        reset_width_action.triggered.connect(self.reset_column_widths)
        menu.addAction(reset_width_action)

        menu.addSeparator()



        # 显示菜单
        menu.exec_(self.table.mapToGlobal(position))

    def auto_resize_columns(self):
        """自动调整列宽"""
        self.table.resizeColumnsToContents()
        self.save_column_widths()
        self.add_log("🔧 已自动调整列宽")

    def reset_column_widths(self):
        """重置列宽为默认值"""
        # 使用与初始化相同的逻辑
        default_widths = []

        # 基础显示列（0-7）
        default_widths.extend([60, 150, 300, 80, 250, 120, 100, 120])

        # 数据库字段（8-26）
        default_widths.extend([100, 120, 150, 200, 150, 100, 120, 100, 100, 150, 120, 120, 200, 80, 80, 120, 120, 120, 80])

        # 原始字段（27-108）
        for i in range(27, 109):
            if i in [42, 49, 62, 74, 78, 82]:  # 重要字段
                default_widths.append(200)
            elif i in [27, 28, 39, 40, 67, 68, 80, 85]:  # 中等重要字段
                default_widths.append(150)
            else:
                default_widths.append(120)

        # 诊断字段（109-112）
        default_widths.extend([150, 150, 150, 150])

        # 嵌套字段详细信息（113-203）
        for i in range(113, 204):
            if 'value' in TABLE_COLUMNS[i] or 'displayname' in TABLE_COLUMNS[i]:
                default_widths.append(150)
            else:
                default_widths.append(100)

        # 应用列宽
        for i, width in enumerate(default_widths):
            if i < self.table.columnCount():
                self.table.setColumnWidth(i, width)

        self.save_column_widths()
        self.add_log("↩️ 已重置列宽为默认值")

    def toggle_column_visibility(self, column, visible):
        """切换列的显示/隐藏"""
        self.table.setColumnHidden(column, not visible)
        column_name = TABLE_COLUMNS[column] if column < len(TABLE_COLUMNS) else f"列{column}"
        status = "显示" if visible else "隐藏"
        self.add_log(f"👁️ {status}列: {column_name}")



    def setup_ui(self):
        """创建用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 统计面板
        stats_group = QGroupBox("📊 告警统计")
        stats_layout = QHBoxLayout(stats_group)

        self.stats_label = QLabel("正在加载...")
        self.stats_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.stats_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        stats_layout.addWidget(self.stats_label)

        # 监控统计信息
        self.monitor_stats_label = QLabel("监控统计: 刷新次数:0 | 运行时间:0秒")
        self.monitor_stats_label.setFont(QFont("Arial", 9))
        self.monitor_stats_label.setStyleSheet("color: #7f8c8d; padding: 5px;")
        stats_layout.addWidget(self.monitor_stats_label)

        # 控制面板
        control_group = QGroupBox("🎛️ 控制面板")
        control_layout = QHBoxLayout(control_group)



        # 实时监控功能已删除

        # 从网管获取数据按钮
        self.fetch_btn = QPushButton("📡 从网管获取")
        self.fetch_btn.clicked.connect(self.fetch_from_web)
        control_layout.addWidget(self.fetch_btn)

        # 配置按钮
        self.config_btn = QPushButton("⚙️ 配置")
        self.config_btn.clicked.connect(self.show_config)
        control_layout.addWidget(self.config_btn)







        # 搜索功能已删除



        # 定时获取控制按钮
        self.timer_btn = QPushButton("⏰ 启用定时获取")
        self.timer_btn.clicked.connect(self.toggle_fetch_timer)
        control_layout.addWidget(self.timer_btn)

        # 筛选选项已删除

        # 邮件设置按钮
        email_btn = QPushButton("📧 邮件设置")
        email_btn.clicked.connect(self.show_email_config)
        control_layout.addWidget(email_btn)



        control_layout.addStretch()

        # 监控状态指示
        self.monitor_status = QLabel("🟢 监控中")
        self.monitor_status.setFont(QFont("Arial", 10, QFont.Bold))
        self.monitor_status.setStyleSheet("color: green; padding: 5px;")
        control_layout.addWidget(self.monitor_status)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：告警表格
        table_group = QGroupBox("📋 告警列表")
        table_layout = QVBoxLayout(table_group)

        self.table = QTableWidget()
        self.table.setColumnCount(len(TABLE_COLUMNS))
        self.table.setHorizontalHeaderLabels(TABLE_COLUMNS)

        # 默认隐藏月度列（只看父级信息 JSON）
        for col_name in ["父级关联2025-04", "父级关联2025-06", "父级关联2025-07", "父级关联2025-08"]:
            try:
                idx = TABLE_COLUMNS.index(col_name)
                self.table.setColumnHidden(idx, True)
            except ValueError:
                pass

        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSortingEnabled(True)
        self.table.verticalHeader().setVisible(False)

        # 性能优化设置
        self.table.setUpdatesEnabled(True)
        self.table.setAutoScroll(True)
        self.table.setVerticalScrollMode(QTableWidget.ScrollPerPixel)  # 平滑滚动
        self.table.setHorizontalScrollMode(QTableWidget.ScrollPerPixel)

        # 设置列宽 - 支持用户拖拽调整
        header = self.table.horizontalHeader()

        # 启用列宽拖拽调整
        header.setSectionsMovable(True)  # 允许拖拽移动列
        header.setSectionsClickable(True)  # 允许点击列头
        header.setDefaultSectionSize(120)  # 默认列宽

        # 设置初始列宽（用户可以拖拽调整）- 完整203列
        # 为了简化，我们为所有203列设置合理的默认宽度
        default_widths = []

        # 基础显示列（0-7）- 重要列设置较宽
        default_widths.extend([60, 150, 300, 80, 250, 120, 100, 120])

        # 数据库字段（8-26）- 常用字段
        default_widths.extend([100, 120, 150, 200, 150, 100, 120, 100, 100, 150, 120, 120, 200, 80, 80, 120, 120, 120, 80])

        # 原始字段（27-108）- 基础宽度
        for i in range(27, 109):
            if i in [42, 49, 62, 74, 78, 82]:  # 重要的原始字段设置较宽
                default_widths.append(200)
            elif i in [27, 28, 39, 40, 67, 68, 80, 85]:  # 中等重要字段
                default_widths.append(150)
            elif i == 66:  # NBI ID列 - 设置较宽以确保可见
                default_widths.append(200)
            else:  # 其他字段
                default_widths.append(120)

        # 诊断字段（109-112）- 诊断相关
        default_widths.extend([150, 150, 150, 150])

        # 嵌套字段详细信息（113-203）- 技术细节字段，设置较窄
        for i in range(113, 204):
            if 'value' in TABLE_COLUMNS[i] or 'displayname' in TABLE_COLUMNS[i]:  # 值和显示名设置较宽
                default_widths.append(150)
            else:  # 其他元数据字段设置较窄
                default_widths.append(100)

        # 应用列宽
        for i, width in enumerate(default_widths):
            if i < self.table.columnCount():
                self.table.setColumnWidth(i, width)

        # 设置列宽调整模式
        header.setSectionResizeMode(QHeaderView.Interactive)  # 允许用户调整所有列宽

        # 连接列宽变化信号，自动保存列宽设置
        header.sectionResized.connect(self.save_column_widths)

        # 恢复保存的列宽设置
        self.restore_column_widths()

        # 应用默认列显示设置（显示全部列）
        # 显示所有列，不隐藏任何列
        for i in range(self.table.columnCount()):
            self.table.setColumnHidden(i, False)  # False = 显示，True = 隐藏

        # 🔧 确保NBI ID列（第66列）可见且有合适宽度
        nbi_id_col = 66
        if nbi_id_col < self.table.columnCount():
            self.table.setColumnHidden(nbi_id_col, False)
            self.table.setColumnWidth(nbi_id_col, 200)
            # 延迟记录日志，等log_text创建后再记录
            self._nbi_id_setup_done = True

        # 双击事件
        self.table.itemDoubleClicked.connect(self.on_item_double_clicked)

        # 设置右键菜单
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_table_context_menu)

        table_layout.addWidget(self.table)

        # 🚀 智能分页控制界面
        pagination_layout = self.create_smart_pagination_ui()
        table_layout.addLayout(pagination_layout)

        # 右侧：日志框
        log_group = QGroupBox("📝 监控日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QPlainTextEdit()
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setMaximumBlockCount(LOG_MAX_LINES)  # 限制最大行数，防止内存泄漏
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # NBI ID设置已完成，稍后在初始化完成后记录日志

        # 日志控制
        log_control_layout = QHBoxLayout()
        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        log_control_layout.addWidget(self.auto_scroll_cb)

        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(clear_log_btn)
        log_control_layout.addStretch()

        log_layout.addLayout(log_control_layout)

        # 添加到分割器
        splitter.addWidget(table_group)
        splitter.addWidget(log_group)
        splitter.setSizes([800, 400])  # 设置初始比例

        # 添加到主布局
        main_layout.addWidget(stats_group)
        main_layout.addWidget(control_group)
        main_layout.addWidget(splitter, 1)  # 分割器占据剩余空间

        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

    def setup_timer(self):
        """设置定时器（默认不启动）"""
        # 实时监控定时器已删除

        # 加载定时获取配置
        self.load_timer_config()

    # 筛选功能已删除

    def refresh_data(self):
        """刷新数据"""
        # 防止重复刷新
        if hasattr(self, '_refreshing') and self._refreshing:
            self.add_log("⚠️ 上次刷新还未完成，跳过本次刷新")
            return

        self._refreshing = True
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 100)  # 确定进度条
        self.progress_bar.setValue(0)
        self.status_bar.showMessage("正在刷新数据...")

        # 停止之前的工作线程（如果存在）
        if hasattr(self, 'worker') and self.worker.isRunning():
            self.add_log("🔄 停止上一个数据加载线程...")
            self.worker.stop()
            if not self.worker.wait(2000):  # 等待2秒
                self.add_log("⚠️ 强制终止上一个线程")
                self.worker.terminate()
                self.worker.wait()

        # 启动工作线程
        self.worker = DataWorker(self.db_file, self.focus_keywords)
        self.worker.finished_ok.connect(self.on_data_ready)
        self.worker.finished_err.connect(self.on_error)
        self.worker.log_message.connect(self.add_log)
        self.worker.progress_update.connect(self.update_progress)
        self.worker.finished.connect(self.on_refresh_finished)
        self.worker.start()

    def on_data_ready(self, alarms, stats):
        """数据加载完成"""
        # 检测新告警
        if hasattr(self, 'alarms_data') and self.alarms_data:
            old_ids = set(alarm['id'] for alarm in self.alarms_data)
            new_ids = set(alarm['id'] for alarm in alarms)
            self.new_alarms_count = len(new_ids - old_ids)
            if self.new_alarms_count > 0:
                self.add_log(f"🆕 检测到 {self.new_alarms_count} 个新告警")
        else:
            self.new_alarms_count = 0

        self.alarms_data = alarms

        # 添加性能监控
        import time
        ui_start = time.time()
        self.update_stats(stats)
        stats_time = time.time() - ui_start

        table_start = time.time()
        # 🚀 使用智能分页更新表格
        self.update_table_with_smart_pagination(alarms)
        table_time = time.time() - table_start

        self.add_log(f"🔧 UI更新耗时: 统计{stats_time:.2f}秒, 表格{table_time:.2f}秒")

        # 获取完成后尝试按规则发送邮件（跳过首次基线）
        email_start = time.time()
        try:
            # 判断是否首次基线：全部为基线且无新增
            total = stats.get('total', 0)
            baseline = stats.get('baseline', 0)
            new_cnt = stats.get('new', 0)
            is_first_baseline = (total > 0 and total == baseline and new_cnt == 0)
            if is_first_baseline:
                self.add_log("📧 跳过邮件发送（全部为历史告警）")
            else:
                # 使用新的拆分发送逻辑
                self.maybe_send_email(alarms)

                # 启用持续告警邮件发送
                self.maybe_send_sustained_emails(alarms)
        except Exception as e:
            self.add_log(f"📧 邮件发送失败: {e}")

        email_time = time.time() - email_start
        self.add_log(f"📧 邮件处理完成，耗时 {email_time:.1f}秒")

        self.last_refresh_time = datetime.now()
        self.refresh_count += 1

        # 更新窗口标题显示统计信息
        displayed_count = len(alarms)
        title = f"🚨 告警监控系统 - 显示:{displayed_count}/{stats['total']} 🆕新:{stats.get('new', 0)} 📊基线:{stats.get('baseline', 0)} 📍持续:{stats.get('continuing', 0)} 🔴根源:{stats['root']} 🟡衍生:{stats['derived']} 🎯重点:{stats['focus']}"
        self.setWindowTitle(title)

        # 记录统计信息到日志 - 完全对齐web.py逻辑
        self.add_log(f"📊 统计更新 - 总计:{stats['total']} 🆕新:{stats.get('new', 0)} 📊基线:{stats.get('baseline', 0)} 📍持续:{stats.get('continuing', 0)} 根源:{stats['root']} 衍生:{stats['derived']} 重点:{stats['focus']}")

        # 如果有新告警，记录到日志
        if self.new_alarms_count > 0:
            self.add_log(f"⚠️ 发现新告警，请注意检查")

    def on_refresh_finished(self):
        """刷新完成"""
        self._refreshing = False  # 重置刷新标志
        self.progress_bar.setVisible(False)

        # 更新状态信息
        status_msg = f"刷新完成 - 显示 {len(self.alarms_data)} 条告警"
        if self.new_alarms_count > 0:
            status_msg += f" - 🆕 新增 {self.new_alarms_count} 条"
        status_msg += f" - {datetime.now().strftime('%H:%M:%S')}"

        self.status_bar.showMessage(status_msg)
        self.add_log(f"✅ {status_msg}")

    # 实时监控状态更新已删除
    def _short_id(self, s: str) -> str:
        if not s:
            return ""
        s = str(s)
        if len(s) <= 6:
            return s
        return s[:6]

    def _group_key_short(self, a):
        """按短ID分组键（用于邮件分组显示）- 修复版本"""
        root_id = (a.get('root_group_id') or '').strip()
        if not root_id:
            # 独立告警：安全获取alarm_key
            ak = self._safe_get_alarm_key(a)
            return f"独立_{self._short_id(ak)}"
        return self._short_id(root_id)

    def _safe_get_alarm_key(self, alarm):
        """安全获取告警键，处理各种数据格式"""
        # 优先使用直接字段
        ak = alarm.get('alarm_key', '')
        if ak:
            return ak

        # 从raw_data安全获取
        raw_data = alarm.get('raw_data', {})
        if isinstance(raw_data, dict):
            ak = raw_data.get('alarmkey', '')
        elif isinstance(raw_data, str) and raw_data.strip():
            try:
                import json
                parsed = json.loads(raw_data)
                ak = parsed.get('alarmkey', '') if isinstance(parsed, dict) else ''
            except (json.JSONDecodeError, ValueError):
                ak = ''

        # 最后的备用方案：使用告警的哈希值
        if not ak:
            ak = f"unknown_{abs(hash(str(alarm)))}"

        return ak

    def _sustained_threshold_key(self, a):
        """持续阈值判断的唯一键（根源ID + 告警ID组合，用于精确判断重复发送）- 修复版本"""
        root_id = (a.get('root_group_id') or '').strip()
        alarm_id = self._safe_get_alarm_id(a)

        if not root_id:
            # 独立告警：优先使用告警ID，降级到alarm_key
            if alarm_id:
                return f"独立_{alarm_id}"
            ak = self._safe_get_alarm_key(a)
            return f"独立_{self._short_id(ak)}"

        if alarm_id:
            # 有根源ID和告警ID：根源ID截取前6位，告警ID使用完整
            return f"{self._short_id(root_id)}_{alarm_id}"
        else:
            # 只有根源ID：保持原逻辑（向后兼容）
            return self._short_id(root_id)

    def _safe_get_alarm_id(self, alarm):
        """安全获取告警ID"""
        # 优先使用直接字段
        alarm_id = alarm.get('raw_id', '')
        if alarm_id:
            return alarm_id

        # 从raw_data安全获取
        raw_data = alarm.get('raw_data', {})
        if isinstance(raw_data, dict):
            return raw_data.get('id', '')
        elif isinstance(raw_data, str) and raw_data.strip():
            try:
                import json
                parsed = json.loads(raw_data)
                return parsed.get('id', '') if isinstance(parsed, dict) else ''
            except (json.JSONDecodeError, ValueError):
                return ''

        return ''

    def _validate_grouping_logic(self, groups):
        """验证分组逻辑的正确性"""
        print(f"🔍 分组验证：共 {len(groups)} 个分组")

        total_alarms = 0
        issues_found = 0

        for group_key, alarms in groups.items():
            total_alarms += len(alarms)
            print(f"  分组 '{group_key}': {len(alarms)} 个告警")

            if group_key.startswith('独立_'):
                # 验证独立告警分组
                for alarm in alarms:
                    relationflag = alarm.get('relationflag', 0)
                    root_group_id = alarm.get('root_group_id', '')
                    alarm_key = alarm.get('alarm_key', '')

                    # 特别调试42d731分组问题
                    if group_key == '独立_42d731':
                        print(f"    🔍 调试42d731分组:")
                        print(f"       告警: {alarm.get('code_name', '未知')}")
                        print(f"       alarm_key: {alarm_key}")
                        print(f"       relationflag: {relationflag}")
                        print(f"       root_group_id: '{root_group_id}'")
                        raw_data = alarm.get('raw_data', {})
                        if isinstance(raw_data, dict):
                            parentinfo = raw_data.get('parentinfo', '')
                            print(f"       parentinfo: {parentinfo}")
                        print(f"       问题分析: root_group_id为空导致被分组为独立")

                    # 只有当relationflag表示关联但root_group_id不为空时才是错误
                    if relationflag in (1, 2, 3) and root_group_id:
                        issues_found += 1
                        print(f"    ⚠️ 关联告警被错误分组为独立:")
                        print(f"       告警: {alarm.get('code_name', '未知')}")
                        print(f"       relationflag: {relationflag}")
                        print(f"       root_group_id: '{root_group_id}'")
                    elif relationflag in (2, 3) and not root_group_id:
                        print(f"    ℹ️ 关联告警因追根失败被分组为独立（预期行为）:")
                        print(f"       告警: {alarm.get('code_name', '未知')}")
                        print(f"       relationflag: {relationflag}")
            else:
                # 验证关联告警分组
                root_ids = set()
                relationflags = set()

                for alarm in alarms:
                    root_id = alarm.get('root_group_id', '')
                    relationflag = alarm.get('relationflag', 0)

                    if root_id:
                        root_ids.add(root_id)
                    relationflags.add(relationflag)

                # 检查root_group_id一致性
                if len(root_ids) > 1:
                    issues_found += 1
                    print(f"    ⚠️ 分组内root_group_id不一致:")
                    print(f"       root_ids: {list(root_ids)}")

                # 检查是否包含根源告警
                if 1 not in relationflags and len(alarms) > 1:
                    print(f"    ℹ️ 分组无根源告警，可能是次根源分组")

        print(f"🔍 验证完成：{total_alarms} 个告警，发现 {issues_found} 个问题")
        return issues_found == 0


    def _build_regular_groups(self, alarms, settings):
        """构建regular邮件的组清单（不发送）。"""
        candidates = self.build_email_candidates(alarms)
        # 按短ID分组（与实际发送一致）
        groups = {}
        for a in candidates:
            gk = self._group_key_short(a)
            groups.setdefault(gk, []).append(a)
        # 组对象
        result = []
        for gk, items in groups.items():
            short = self._short_id(gk) if gk else '无根源ID'
            subject = f"[告警通知] 组:{short} 共{len(items)}条"
            result.append({
                'type': 'regular',
                'group_id': gk,
                'group_short': short,
                'subject': subject,
                'to': settings.get('to_addr', ''),
                'count': len(items),
                'items': items,
            })
        return result

    def _build_sustained_groups(self, alarms, settings):
        """构建sustained邮件的组清单（不发送）。"""
        # 复用 maybe_send_sustained_emails 的筛选逻辑（简化复制）
        sustained = []
        for a in alarms:
            if not (a.get('is_active', 1) == 1 and a.get('is_new', 0) == 0):
                continue
            is_unicom = '联通' in str(a.get('operator_info', ''))
            is_focus = any(kw in (a.get('code_name', '') or '') for kw in self.focus_keywords)
            is_related = a.get('relationflag', 0) in (1, 2, 3)
            if not (is_unicom and (is_focus or is_related)):
                continue
            minutes = self._get_effective_minutes_for_alarm(a)
            # 超5天过滤
            if minutes >= 7200:
                continue
            crossed = [t for t in SUSTAINED_THRESHOLDS_MINUTES if minutes >= t]
            if not crossed:
                continue
            a['_sustained_threshold'] = max(crossed)
            a['_effective_minutes'] = minutes
            sustained.append(a)
        # 分组（短ID）
        groups = {}
        for a in sustained:
            gk = self._group_key_short(a)
            groups.setdefault(gk, []).append(a)
        # 构建分组对象：按本轮最高阈值
        result = []
        for gk, items in groups.items():
            # 超5天整组过滤（再检查一次）
            if any((x.get('_effective_minutes') or 0) >= 7200 for x in items):
                continue
            highest = max([x.get('_sustained_threshold', 0) for x in items] or [0])
            if highest <= 0:
                continue
            short = self._short_id(gk) if gk else '无根源ID'
            current_time = datetime.now().strftime('%H:%M:%S')
            subject = f"[告警通知][持续阈值 {highest//60 if highest>=60 else highest}h] 组:{short} 共{len(items)} {current_time}"
            result.append({
                'type': 'sustained',
                'group_id': gk,
                'group_short': short,
                'subject': subject,
                'to': settings.get('to_addr', ''),
                'count': len(items),
                'highest_threshold': highest,
                'items': items,
            })
        return result



    def show_email_config(self):
        try:
            dlg = EmailConfigDialog(self, CONFIG_FILE)
            dlg.exec()
        except Exception as e:
            self.add_log(f"📧 打开邮件设置失败: {e}")

    # load_email_settings方法已被EmailManager替代，保持向后兼容
    def load_email_settings(self):
        """向后兼容方法，实际使用EmailManager"""
        if self.email_manager:
            return self.email_manager.get_settings()
        else:
            # 如果EmailManager未初始化，返回默认配置
            return EMAIL_DEFAULTS.copy()

    def build_email_candidates(self, alarms):
        candidates = []
        try:
            for a in alarms:
                is_new_or_baseline = (a.get('is_new', 0) == 1) or (a.get('is_baseline', 0) == 1)
                code_name = a.get('code_name', '') or ''
                is_focus = any(kw in code_name for kw in self.focus_keywords)
                relationflag = a.get('relationflag', 0)
                is_related = relationflag in (1, 2, 3)
                is_unicom = str(a.get('operator_info', '')).find('联通') >= 0
                if is_new_or_baseline and is_unicom and (is_focus or is_related):
                    key = a.get('alarm_key') or a.get('raw_data', {}).get('alarmkey')
                    if key and key not in self.sent_email_keys:
                        # 快速时间检查：超过5天（7200分钟）不发送
                        if self._is_alarm_too_old(a):
                            continue
                        candidates.append(a)
        except Exception as e:
            self.add_log(f"📧 组装候选失败: {e}")
        return candidates

    def compose_email(self, candidates):
        """创建新告警邮件 - Markdown格式"""
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # 安全处理文本内容，避免特殊字符导致SMTP错误
        def safe_text(text):
            if not text:
                return ""
            # 移除可能导致SMTP问题的特殊字符
            text = str(text).replace('\r', '').replace('\n', ' ').replace('\t', ' ')
            # 不再限制长度，允许完整内容显示
            return text

        # 统计告警类型
        root_count = len([a for a in candidates if a.get('relationflag') == 1])
        sub_root_count = len([a for a in candidates if a.get('relationflag') == 3])
        derived_count = len([a for a in candidates if a.get('relationflag') == 2])
        independent_count = len([a for a in candidates if a.get('relationflag') == 0])

        # 获取时间范围
        times = [a.get('time_str', '') for a in candidates if a.get('time_str')]
        time_range = f"{min(times)} ~ {max(times)}" if len(times) > 1 else (times[0] if times else "")

        # 获取第一个告警的网元名称
        first_me_name = "未知网元"
        if candidates:
            first_me_name = candidates[0].get('me_name', '未知网元')

        # 按关联标志分组告警名称
        derived_alarms = [a.get('code_name', '') for a in candidates if a.get('relationflag') == 2]
        sub_root_alarms = [a.get('code_name', '') for a in candidates if a.get('relationflag') == 3]
        root_alarms = [a.get('code_name', '') for a in candidates if a.get('relationflag') == 1]
        independent_alarms = [a.get('code_name', '') for a in candidates if a.get('relationflag') == 0]

        # 去重并格式化告警名称
        alarm_names = []
        if derived_alarms:
            unique_derived = list(set(derived_alarms))
            alarm_names.append(f"【衍生】{', '.join(unique_derived[:3])}{'...' if len(unique_derived) > 3 else ''}")
        if sub_root_alarms:
            unique_sub_root = list(set(sub_root_alarms))
            alarm_names.append(f"【次根源】{', '.join(unique_sub_root[:3])}{'...' if len(unique_sub_root) > 3 else ''}")
        if root_alarms:
            unique_root = list(set(root_alarms))
            alarm_names.append(f"【根源】{', '.join(unique_root[:3])}{'...' if len(unique_root) > 3 else ''}")
        if independent_alarms:
            unique_independent = list(set(independent_alarms))
            alarm_names.append(f"【独立】{', '.join(unique_independent[:3])}{'...' if len(unique_independent) > 3 else ''}")

        alarm_summary = ' - '.join(alarm_names) if alarm_names else '无分类告警'

        # 生成Markdown内容
        markdown_content = f"""新告警通知 - {datetime.now().strftime('%Y年%m月%d日')}

网元: {safe_text(first_me_name)}
告警: {alarm_summary}

总计 {len(candidates)} 条 | 🔴根源 {root_count} 条 | 🟠次根源 {sub_root_count} 条 | 🟡衍生 {derived_count} 条 | 🟢独立 {independent_count} 条
时间: {time_range}

| 位置信息 | 时间 | 告警名称 | 关联 | 产品资源类型 | RAN对象类型 | RAN板类型 | 根源ID |
|----------|------|----------|------|-------------|------------|----------|--------|"""

        # 按关联标志排序：根源->次根源->衍生->独立
        def get_sort_key(alarm):
            relationflag = alarm.get('relationflag', 0)
            if relationflag == 1: return (0, alarm.get('time_str', ''))  # 根源
            elif relationflag == 3: return (1, alarm.get('time_str', ''))  # 次根源
            elif relationflag == 2: return (2, alarm.get('time_str', ''))  # 衍生
            else: return (3, alarm.get('time_str', ''))  # 独立

        sorted_candidates = sorted(candidates[:100], key=get_sort_key)

        for i, a in enumerate(sorted_candidates, 1):
            # 获取关联标志信息
            relationflag = a.get('relationflag', 0)
            if relationflag == 1:
                relation_text = "🔴根源"
            elif relationflag == 3:
                relation_text = "🟠次根源"
            elif relationflag == 2:
                relation_text = "🟡衍生"
            else:
                relation_text = "🟢独立"

            # 获取详细信息字段
            position_name = a.get('raw_positionname', '')
            product_restype = a.get('raw_productrestype', '')
            ran_object_type = a.get('raw_ran_fm_alarm_object_type', '')
            ran_board_type = a.get('raw_ran_fm_alarm_board_type', '')

            markdown_content += f"""
| {safe_text(position_name)} | {safe_text(a.get('time_str', ''))} | **{safe_text(a.get('code_name', ''))}** | {relation_text} | {safe_text(product_restype)} | {safe_text(ran_object_type)} | {safe_text(ran_board_type)} | {safe_text(a.get('root_group_id_short', '') or '')} |"""



        msg = MIMEMultipart('alternative')
        # 统一邮件主题格式：新-网元名称-月日 时:分:秒
        timestamp = self._format_email_timestamp()
        safe_me_name = self._safe_subject_text(first_me_name, 20)
        safe_subject = f"新-{safe_me_name}-{timestamp}"

        # 设置完整的邮件头部信息
        import uuid
        from email.utils import formatdate, make_msgid

        msg['Subject'] = safe_subject
        msg['Date'] = formatdate(localtime=True)  # 当前时间的Date头
        msg['Message-ID'] = make_msgid(domain='alarm-monitor.local')  # 唯一Message-ID
        msg['X-Mailer'] = 'AlarmMonitor/2.0'
        msg['X-Priority'] = '2'  # 高优先级
        msg['Importance'] = 'High'
        # 移除可能触发垃圾邮件过滤的头部
        # msg['Auto-Submitted'] = 'auto-generated'  # 高风险：标识为自动生成
        # msg['Precedence'] = 'bulk'  # 高风险：批量邮件标识
        # msg['List-Unsubscribe'] = '<mailto:<EMAIL>>'  # 中风险：退订链接
        # msg['X-Auto-Response-Suppress'] = 'All'  # 中风险：抑制自动回复

        # 添加更安全的邮件头
        msg['X-Alarm-System'] = 'Network-Monitor'  # 自定义标识
        msg['X-Message-Type'] = 'Alert-Notification'  # 消息类型标识

        # 添加UUID随机注释降低内容相似度
        random_uuid = str(uuid.uuid4())
        enhanced_content = f"{markdown_content}\n\n<!-- UUID: {random_uuid} -->"

        # 🔐 Linus式加密处理 - 简单开关控制
        final_content = enhanced_content
        if self.email_manager and self.email_manager.is_encryption_enabled():
            try:
                encrypted_content, is_encrypted = self.email_manager.encrypt_email_content(enhanced_content)
                if is_encrypted:
                    # 构建加密邮件格式
                    encryption_settings = self.email_manager.get_encryption_settings()
                    password = encryption_settings.get('encryption_password', '')

                    final_content = f"""
🔐 此邮件内容已加密

-----BEGIN ENCRYPTED CONTENT-----
{encrypted_content}
-----END ENCRYPTED CONTENT-----
"""

                    # 如果启用了解密说明，添加解密指导
                    if encryption_settings.get('include_decrypt_info', True):
                        decrypt_info = EmailEncryption.create_decrypt_instructions(password)
                        final_content += decrypt_info

                    self.add_log("🔐 邮件内容已加密")
                else:
                    self.add_log("⚠️ 加密失败，使用明文发送")
            except Exception as e:
                self.add_log(f"🔐 加密处理失败: {e}")
                # 加密失败时使用原内容
        else:
            self.add_log("📧 邮件内容使用明文发送")

        msg.attach(MIMEText(final_content, 'plain', 'utf-8'))
        return msg

    def compose_email_with_groups(self, groups):
        """创建包含多个分组的新告警邮件 - 纯文本格式"""
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # 安全处理文本内容，避免特殊字符导致SMTP错误
        def safe_text(text):
            if not text:
                return ""
            # 移除可能导致SMTP问题的特殊字符
            text = str(text).replace('\r', '').replace('\n', ' ').replace('\t', ' ')
            # 不再限制长度，允许完整内容显示
            return text

        # 统计总体信息
        total_alarms = sum(len(group_list) for group_list in groups.values())
        total_groups = len(groups)

        # 获取第一个告警的网元名称作为邮件主题
        first_alarm = None
        for group_list in groups.values():
            if group_list:
                first_alarm = group_list[0]
                break

        first_me_name = safe_text(first_alarm.get('me_name', '未知网元')) if first_alarm else '未知网元'

        # 生成详细的文本内容
        content_lines = []
        content_lines.append(f"新告警通知 - {first_me_name}")
        content_lines.append("")
        content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_lines.append(f"告警总数: {total_alarms} 条")
        content_lines.append(f"分组数量: {total_groups} 组")
        content_lines.append("")
        content_lines.append("=" * 60)
        content_lines.append("")

        # 添加每个分组的详细信息
        for group_key, group_alarms in groups.items():
            content_lines.append(f"分组: {group_key} ({len(group_alarms)} 条告警)")
            content_lines.append("-" * 40)

            for i, alarm in enumerate(group_alarms, 1):
                content_lines.append(f"告警 {i}:")
                content_lines.append(f"  告警名称: {alarm.get('code_name', '未知告警')}")
                content_lines.append(f"  网元名称: {alarm.get('me_name', '未知网元')}")
                # 位置信息（带翻译）
                position_info = alarm.get('position_name', '未知位置')
                translated_position = self.PositionTranslator.safe_translate_position(position_info)
                content_lines.append(f"  位置信息: {translated_position}")
                content_lines.append(f"  发生时间: {alarm.get('time_str', '未知时间')}")

                # 持续时间信息
                duration_str = alarm.get('duration_str', '')
                if duration_str and duration_str != '未知':
                    content_lines.append(f"  持续时间: {duration_str}")

                # 关联标记
                relation_marks = alarm.get('relation_marks', '')
                if relation_marks:
                    content_lines.append(f"  关联标记: {relation_marks}")

                content_lines.append("")

            content_lines.append("")

        content_lines.append("=" * 60)
        content_lines.append("")
        content_lines.append("系统信息:")
        content_lines.append("- 告警监控系统自动发送")
        content_lines.append("- 请及时处理相关告警")
        content_lines.append("")
        content_lines.append("如有疑问，请联系系统管理员。")

        simple_text = "\n".join(content_lines)

        # 创建邮件对象
        msg = MIMEMultipart('mixed')  # 使用mixed类型支持附件

        # 生成邮件主题 - 使用统一格式
        timestamp = self._format_email_timestamp()
        safe_me_name = self._safe_subject_text(first_me_name, 20)
        safe_subject = f"新-{safe_me_name}-{timestamp}"

        # 设置完整的邮件头部信息
        import uuid
        from email.utils import formatdate, make_msgid

        msg['Subject'] = safe_subject
        msg['Date'] = formatdate(localtime=True)
        msg['Message-ID'] = make_msgid(domain='alarm-monitor.local')
        msg['X-Mailer'] = 'AlarmMonitor/2.0'
        msg['X-Priority'] = '2'  # 高优先级
        msg['Importance'] = 'High'
        msg['Auto-Submitted'] = 'auto-generated'
        msg['Precedence'] = 'bulk'
        msg['List-Unsubscribe'] = '<mailto:<EMAIL>>'
        msg['X-Auto-Response-Suppress'] = 'All'

        # 添加文本内容
        random_uuid = str(uuid.uuid4())
        enhanced_text = f"{simple_text}\n\n<!-- UUID: {random_uuid} -->"

        # 🔐 Linus式加密处理 - 与其他邮件方法保持一致
        final_content = enhanced_text
        if self.email_manager and self.email_manager.is_encryption_enabled():
            try:
                encrypted_content, is_encrypted = self.email_manager.encrypt_email_content(enhanced_text)
                if is_encrypted:
                    # 构建加密邮件格式
                    encryption_settings = self.email_manager.get_encryption_settings()
                    password = encryption_settings.get('encryption_password', '')

                    final_content = f"""
🔐 此邮件内容已加密

-----BEGIN ENCRYPTED CONTENT-----
{encrypted_content}
-----END ENCRYPTED CONTENT-----
"""

                    # 如果启用了解密说明，添加解密指导
                    if encryption_settings.get('include_decrypt_info', True):
                        decrypt_info = EmailEncryption.create_decrypt_instructions(password)
                        final_content += decrypt_info

                    self.add_log("🔐 新告警邮件内容已加密")
                else:
                    self.add_log("⚠️ 新告警邮件加密失败，使用明文发送")
            except Exception as e:
                self.add_log(f"🔐 新告警邮件加密处理失败: {e}")
                # 加密失败时使用原内容
        else:
            self.add_log("📧 新告警邮件内容使用明文发送")

        msg.attach(MIMEText(final_content, 'plain', 'utf-8'))

        return msg



    def _format_email_timestamp(self):
        """统一的邮件时间戳格式 - 消除时间格式不一致"""
        return datetime.now().strftime('%m%d %H:%M:%S')

    def _safe_subject_text(self, text, max_length=30):
        """安全处理邮件主题文本 - 移除特殊字符并限制长度"""
        if not text:
            return '未知'
        safe_text = str(text).replace('\r', '').replace('\n', '').replace('\t', '')
        return safe_text[:max_length] if len(safe_text) > max_length else safe_text

    def _normalize_alarm_time(self, time_value):
        """统一的告警时间标准化处理 - 解决时间格式不一致问题"""
        if not time_value or time_value == '-':
            return '-', '-'

        try:
            # 情况1：数字时间戳（秒或毫秒）
            if isinstance(time_value, (int, float)):
                timestamp = time_value
                if timestamp > 1e12:  # 毫秒时间戳
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                excel_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                text_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                return excel_format, text_format

            # 情况2：字符串时间戳
            if isinstance(time_value, str) and time_value.isdigit():
                timestamp = float(time_value)
                if timestamp > 1e12:  # 毫秒时间戳
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                excel_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                text_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                return excel_format, text_format

            # 情况3：ISO格式字符串 "2024-08-18T10:30:00.123Z"
            if isinstance(time_value, str) and 'T' in time_value:
                # 移除毫秒和时区信息，只保留前19位
                clean_time = time_value[:19]
                dt = datetime.strptime(clean_time, "%Y-%m-%dT%H:%M:%S")
                excel_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                text_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                return excel_format, text_format

            # 情况4：标准格式字符串 "2024-08-18 10:30:00"
            if isinstance(time_value, str) and len(time_value) >= 19:
                clean_time = time_value[:19]
                dt = datetime.strptime(clean_time, "%Y-%m-%d %H:%M:%S")
                excel_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                text_format = dt.strftime("%Y-%m-%d %H:%M:%S")
                return excel_format, text_format

            # 情况5：其他格式，直接返回字符串
            time_str = str(time_value)[:19] if len(str(time_value)) > 19 else str(time_value)
            return time_str, time_str

        except Exception as e:
            self.add_log(f"⚠️ 时间格式化失败: {time_value} -> {e}")
            return str(time_value), str(time_value)

    def _check_email_size_risk(self, groups):
        """检查邮件大小风险 - 防止邮件过大被拒收"""
        total_alarms = sum(len(group_list) for group_list in groups.values())

        # 估算邮件大小风险
        estimated_image_height = total_alarms * 22 + 500  # 每行22像素 + 边距
        estimated_image_size_mb = (1200 * estimated_image_height * 3) / (1024 * 1024)  # PNG估算
        estimated_total_size_mb = estimated_image_size_mb + 2  # 加上Excel等附件

        risk_level = "低"
        if estimated_total_size_mb > 20:
            risk_level = "高"
        elif estimated_total_size_mb > 10:
            risk_level = "中"

        self.add_log(f"📊 邮件大小评估: {total_alarms}条告警, 预估{estimated_total_size_mb:.1f}MB, 风险:{risk_level}")

        # 高风险时建议分割
        if risk_level == "高":
            self.add_log(f"⚠️ 邮件过大风险: 建议启用告警数量限制或分批发送")
            return True, estimated_total_size_mb

        return False, estimated_total_size_mb

    class TimeLimitManager:
        """独立的时间限制管理模块 - 控制定时获取的时间段"""

        @staticmethod
        def load_time_limit_config():
            """加载时间限制配置"""
            try:
                import configparser
                cfg = configparser.ConfigParser()
                cfg.read('monitor_config.ini', encoding='utf-8')

                # 默认配置
                config = {
                    'enabled': False,
                    'start_time': '23:59',  # 限制开始时间
                    'end_time': '06:00',    # 限制结束时间
                }

                if cfg.has_section('time_limit'):
                    config['enabled'] = cfg.getboolean('time_limit', 'enabled', fallback=False)
                    config['start_time'] = cfg.get('time_limit', 'start_time', fallback='23:59')
                    config['end_time'] = cfg.get('time_limit', 'end_time', fallback='06:00')

                return config
            except Exception:
                # 配置加载失败，返回默认配置（禁用状态）
                return {'enabled': False, 'start_time': '23:59', 'end_time': '06:00'}

        @staticmethod
        def is_time_restricted():
            """检查当前时间是否在限制时间段内"""
            try:
                config = TimeLimitManager.load_time_limit_config()

                # 如果功能未启用，不限制
                if not config['enabled']:
                    return False, None

                from datetime import datetime, time

                # 解析时间配置
                start_time_str = config['start_time']
                end_time_str = config['end_time']

                # 解析时间字符串
                start_hour, start_minute = map(int, start_time_str.split(':'))
                end_hour, end_minute = map(int, end_time_str.split(':'))

                start_time = time(start_hour, start_minute)
                end_time = time(end_hour, end_minute)

                # 获取当前时间
                now = datetime.now()
                current_time = now.time()

                # 判断是否在限制时间段内
                if start_time <= end_time:
                    # 同一天内的时间段，如 09:00-17:00
                    is_restricted = start_time <= current_time <= end_time
                else:
                    # 跨天的时间段，如 23:59-06:00
                    is_restricted = current_time >= start_time or current_time <= end_time

                if is_restricted:
                    # 计算下次允许的时间
                    if start_time <= end_time:
                        # 同一天，下次允许时间是今天的end_time之后
                        next_allowed = datetime.combine(now.date(), end_time)
                        if next_allowed <= now:
                            # 如果已经过了今天的结束时间，下次允许时间是明天的结束时间之后
                            from datetime import timedelta
                            next_allowed = next_allowed + timedelta(days=1)
                    else:
                        # 跨天，下次允许时间是今天的end_time（如果还没到）或明天的end_time
                        next_allowed = datetime.combine(now.date(), end_time)
                        if current_time > end_time:
                            # 如果已经过了今天的结束时间，下次允许时间是明天的结束时间
                            from datetime import timedelta
                            next_allowed = next_allowed + timedelta(days=1)

                    return True, next_allowed
                else:
                    return False, None

            except Exception:
                # 任何异常都不限制，确保不影响正常功能
                return False, None

        @staticmethod
        def get_restriction_info():
            """获取时间限制的详细信息"""
            try:
                config = TimeLimitManager.load_time_limit_config()

                if not config['enabled']:
                    return "时间限制功能已关闭"

                is_restricted, next_allowed = TimeLimitManager.is_time_restricted()

                if is_restricted:
                    if next_allowed:
                        return f"当前处于限制时间段 ({config['start_time']}-{config['end_time']})，下次允许获取时间: {next_allowed.strftime('%H:%M')}"
                    else:
                        return f"当前处于限制时间段 ({config['start_time']}-{config['end_time']})"
                else:
                    return f"当前不在限制时间段内，限制时间: {config['start_time']}-{config['end_time']}"

            except Exception:
                return "时间限制功能异常，已禁用"

    class PositionTranslator:
        """独立的位置信息翻译模块 - 渐进式翻译"""

        # 基础词汇表（可逐步扩展）
        BASIC_TERMS = {
            # 设备层级
            'Equipment': '设备',
            'Rack': '机架',
            'SubRack': '子架',
            'Shelf': '架子',
            'Frame': '框架',

            # 插槽相关
            'Slot': '槽位',
            'PlugInUnit': '插件',
            'ReplaceableUnit': '槽位',
            'Card': '板卡',
            'Board': '板',

            # 端口相关
            'RiPort': '端口',
            'Port': '端口',
            'Interface': '接口',
            'Connector': '连接器',

            # 光纤相关
            'FiberDeviceSet': '光纤设备组',
            'FiberDevice': '光纤设备',
            'OpticalPort': '光端口',
            'OpticalInterface': '光接口',

            # 无线相关
            'SdrDeviceGroup': 'SDR设备组',
            'AntennaPort': '天线端口',
            'RfPort': '射频端口',

            # 其他常见术语
            'Unit': '单元',
            'Module': '模块',
            'Component': '组件',
            'Device': '设备',
            'Group': '组',
            'Set': '集合'
        }

        @staticmethod
        def translate_position(position_text):
            """翻译位置信息，翻译不了就返回原文"""
            if not position_text or not isinstance(position_text, str):
                return position_text

            try:
                # 如果不包含等号，可能不是标准格式，直接返回
                if '=' not in position_text:
                    return position_text

                # 解析键值对
                parts = position_text.split(',')
                translated_parts = []

                for part in parts:
                    part = part.strip()
                    if '=' in part:
                        key, value = part.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        # 翻译键名
                        translated_key = PositionTranslator.BASIC_TERMS.get(key, key)

                        # 如果值是数字，保持原样；如果是字符串，可能需要特殊处理
                        if value.isdigit():
                            translated_parts.append(f"{translated_key}{value}")
                        else:
                            # 对于非数字值，保持原格式但翻译键名
                            translated_parts.append(f"{translated_key}={value}")
                    else:
                        # 不是键值对格式，保持原样
                        translated_parts.append(part)

                # 用 > 连接，更直观
                translated_text = ' > '.join(translated_parts)

                # 如果翻译后的文本与原文差异很大（说明翻译可能有问题），返回原文
                if len(translated_text) > len(position_text) * 2:
                    return position_text

                return translated_text

            except Exception:
                # 任何异常都返回原文，确保不影响邮件发送
                return position_text

        @staticmethod
        def safe_translate_position(position_text):
            """安全翻译：显示中文翻译，同时保留原文"""
            if not position_text or not isinstance(position_text, str):
                return position_text

            try:
                translated = PositionTranslator.translate_position(position_text)

                # 如果翻译后与原文相同，说明没有翻译，只显示原文
                if translated == position_text:
                    return position_text

                # 如果翻译成功，显示中文翻译
                return translated

            except Exception:
                # 异常时返回原文
                return position_text

    class EmailImageGenerator:
        """独立的邮件图片生成模块 - 可选功能"""

        @staticmethod
        def create_text_image(text_content, width=800):
            """将文本内容生成为白底黑字图片"""
            try:
                from PIL import Image, ImageDraw, ImageFont
                import io

                # 尝试加载字体
                try:
                    # Windows系统字体
                    font = ImageFont.truetype("msyh.ttc", 14)  # 微软雅黑
                except:
                    try:
                        font = ImageFont.truetype("arial.ttf", 14)  # Arial
                    except:
                        font = ImageFont.load_default()  # 默认字体

                # 分割文本为行
                lines = text_content.split('\n')

                # 计算图片尺寸
                line_height = 20
                padding = 20
                height = len(lines) * line_height + padding * 2

                # 创建白底图片
                img = Image.new('RGB', (width, height), 'white')
                draw = ImageDraw.Draw(img)

                # 绘制文本
                y = padding
                for line in lines:
                    draw.text((padding, y), line, fill='black', font=font)
                    y += line_height

                # 保存到内存
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='PNG', quality=95)
                img_buffer.seek(0)

                return img_buffer

            except ImportError:
                # PIL库未安装
                return None
            except Exception as e:
                # 其他错误
                print(f"图片生成失败: {e}")
                return None

    def _load_fonts_DELETED(self):
        """统一的字体加载逻辑 - 消除重复的try-except嵌套"""
        try:
            from PIL import ImageFont
        except ImportError:
            return None

        for font_name, sizes in self.ImageConfig.FONT_CANDIDATES:
            try:
                return {
                    'title': ImageFont.truetype(font_name, sizes['title']),
                    'header': ImageFont.truetype(font_name, sizes['header']),
                    'text': ImageFont.truetype(font_name, sizes['text']),
                    'small': ImageFont.truetype(font_name, sizes['small']),
                }
            except:
                continue

        # 最后fallback到默认字体
        default_font = ImageFont.load_default()
        return {
            'title': default_font,
            'header': default_font,
            'text': default_font,
            'small': default_font,
        }

    def create_alarm_image_DELETED(self, groups):
        """生成告警信息图片"""
        try:
            from PIL import Image, ImageDraw
            import io
        except ImportError:
            self.add_log("❌ 缺少PIL库，无法生成图片，请安装: pip install Pillow")
            return None

        fonts = self._load_fonts()
        if not fonts:
            self.add_log("❌ 字体加载失败")
            return None

        config = self.ImageConfig

        # 精确计算图片高度 - 适应完整告警列表显示
        total_alarms = sum(len(group_list) for group_list in groups.values())
        # 增加额外空间以适应完整的告警列表和类型统计
        type_stats_height = len(groups) * 75  # 每组的类型统计区域高度
        actual_height = (config.HEADER_HEIGHT +
                        len(groups) * config.GROUP_SPACING +
                        total_alarms * config.LINE_HEIGHT +
                        type_stats_height +  # 类型统计额外高度
                        config.MARGIN * 6)   # 增加边距以适应更多内容

        # 一次性创建正确大小的图片
        img = Image.new('RGB', (config.WIDTH, actual_height), config.COLORS['bg'])
        draw = ImageDraw.Draw(img)

        current_y = config.MARGIN

        # 绘制标题
        title_text = f"新告警通知 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        draw.text((config.MARGIN, current_y), title_text, fill=config.COLORS['header'], font=fonts['title'])
        current_y += 40

        # 绘制统计信息
        stats_text = f"告警总数: {total_alarms} 条  |  分组数量: {len(groups)} 组"
        draw.text((config.MARGIN, current_y), stats_text, fill=config.COLORS['text'], font=fonts['header'])
        current_y += 50

        # 绘制分割线
        draw.line([(config.MARGIN, current_y), (config.WIDTH - config.MARGIN, current_y)],
                 fill=config.COLORS['header'], width=2)
        current_y += 20

        # 绘制各个分组
        for group_key, group_list in groups.items():
            # 组标题背景
            group_title_rect = [config.MARGIN, current_y, config.WIDTH - config.MARGIN, current_y + 35]
            draw.rectangle(group_title_rect, fill=config.COLORS['group_bg'])

            # 组标题
            group_display_name = group_key if not group_key.startswith('独立_') else '独立告警'
            group_title = f"📋 {group_display_name} ({len(group_list)}条)"
            draw.text((config.MARGIN + 10, current_y + 8), group_title,
                     fill=config.COLORS['header'], font=fonts['header'])
            current_y += 45

            # 统计告警类型
            root_alarms = [a.get('code_name', '') for a in group_list if a.get('relationflag') == 1]
            sub_root_alarms = [a.get('code_name', '') for a in group_list if a.get('relationflag') == 3]
            derived_alarms = [a.get('code_name', '') for a in group_list if a.get('relationflag') == 2]

            # 显示告警类型统计
            if root_alarms:
                type_text = f"🔴 根源告警: {', '.join(set(root_alarms[:3]))}"
                if len(set(root_alarms)) > 3:
                    type_text += f" 等{len(set(root_alarms))}种"
                draw.text((config.MARGIN + 20, current_y), type_text,
                         fill=config.COLORS['root'], font=fonts['text'])
                current_y += 25

            if sub_root_alarms:
                type_text = f"🟡 次根源告警: {', '.join(set(sub_root_alarms[:3]))}"
                if len(set(sub_root_alarms)) > 3:
                    type_text += f" 等{len(set(sub_root_alarms))}种"
                draw.text((config.MARGIN + 20, current_y), type_text,
                         fill=config.COLORS['sub_root'], font=fonts['text'])
                current_y += 25

            if derived_alarms:
                type_text = f"🔵 衍生告警: {', '.join(set(derived_alarms[:3]))}"
                if len(set(derived_alarms)) > 3:
                    type_text += f" 等{len(set(derived_alarms))}种"
                draw.text((config.MARGIN + 20, current_y), type_text,
                         fill=config.COLORS['derived'], font=fonts['text'])
                current_y += 25

            current_y += 10

            # 显示所有详细告警信息（无数量限制）
            for i, alarm in enumerate(group_list, 1):
                code_name = alarm.get('code_name', '') or '未知告警'  # 移除长度限制
                me_name = alarm.get('me_name', '') or '未知网元'      # 移除长度限制
                ne_ip = alarm.get('ne_ip', '') or '未知IP'
                severity = alarm.get('perceived_severity_name', '') or '未知'

                # 根据告警类型选择颜色
                if alarm.get('relationflag') == 1:
                    alarm_color = config.COLORS['root']
                elif alarm.get('relationflag') == 3:
                    alarm_color = config.COLORS['sub_root']
                else:
                    alarm_color = config.COLORS['derived']

                alarm_text = f"{i}. {code_name} | {me_name} | {ne_ip} | {severity}"
                draw.text((config.MARGIN + 30, current_y), alarm_text,
                         fill=alarm_color, font=fonts['small'])
                current_y += 22

            # 移除省略信息显示逻辑，显示完整列表

            current_y += config.GROUP_SPACING

        # 绘制底部信息
        current_y += 20
        draw.line([(config.MARGIN, current_y), (config.WIDTH - config.MARGIN, current_y)],
                 fill=config.COLORS['header'], width=1)
        current_y += 15

        footer_text = "告警监控系统自动生成 | 请及时处理相关告警"
        draw.text((config.MARGIN, current_y), footer_text,
                 fill=config.COLORS['text'], font=fonts['small'])

        # 保存到内存 - 不再需要裁剪，因为我们已经计算了精确尺寸
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG', quality=95)
        img_buffer.seek(0)

        return img_buffer

    def create_sustained_alarm_image_DELETED(self, groups_with_thresholds):
        """生成持续告警信息图片 - 使用统一配置"""
        try:
            from PIL import Image, ImageDraw
            import io
        except ImportError:
            self.add_log("❌ 缺少PIL库，无法生成持续告警图片")
            return None

        fonts = self._load_fonts()
        if not fonts:
            self.add_log("❌ 字体加载失败")
            return None

        config = self.ImageConfig

        # 持续告警使用更宽的布局和紧急色彩
        sustained_width = 1400
        sustained_colors = {
            **config.COLORS,
            'header': config.COLORS['sustained'],  # 深红色更紧急
            'group_bg': (254, 245, 245),  # 浅红色组背景
        }

        # 精确计算图片高度 - 适应完整持续告警列表显示
        total_alarms = sum(len(items) for items in groups_with_thresholds.values())
        # 增加额外空间以适应完整的持续告警列表
        actual_height = (config.HEADER_HEIGHT +
                        len(groups_with_thresholds) * config.GROUP_SPACING +
                        total_alarms * config.LINE_HEIGHT +
                        config.MARGIN * 6)   # 增加边距以适应更多内容

        # 创建图片
        img = Image.new('RGB', (sustained_width, actual_height), sustained_colors['bg'])
        draw = ImageDraw.Draw(img)

        current_y = config.MARGIN

        # 绘制标题
        title_text = f"持续告警通知 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        draw.text((config.MARGIN, current_y), title_text,
                 fill=sustained_colors['header'], font=fonts['title'])
        current_y += 40

        # 绘制统计信息
        stats_text = f"持续告警总数: {total_alarms} 条  |  分组数量: {len(groups_with_thresholds)} 组"
        draw.text((config.MARGIN, current_y), stats_text,
                 fill=sustained_colors['text'], font=fonts['header'])
        current_y += 50

        # 绘制分割线
        draw.line([(config.MARGIN, current_y), (sustained_width - config.MARGIN, current_y)],
                 fill=sustained_colors['header'], width=2)
        current_y += 20

        # 绘制各个分组
        for group_key, (group_items, max_threshold) in groups_with_thresholds.items():
            # 组标题背景
            group_title_rect = [config.MARGIN, current_y, sustained_width - config.MARGIN, current_y + 35]
            draw.rectangle(group_title_rect, fill=sustained_colors['group_bg'])

            # 组标题和阈值信息
            first_me_name = group_items[0].get('me_name', '未知网元') if group_items else '未知网元'
            threshold_hours = max_threshold // 60 if max_threshold >= 60 else max_threshold
            threshold_unit = "小时" if max_threshold >= 60 else "分钟"

            group_title = f"🚨 {first_me_name} - 超{threshold_hours}{threshold_unit} ({len(group_items)}条)"
            draw.text((config.MARGIN + 10, current_y + 8), group_title,
                     fill=sustained_colors['header'], font=fonts['header'])
            current_y += 45

            # 显示所有详细告警信息（无数量限制）
            for i, alarm in enumerate(group_items, 1):
                code_name = alarm.get('code_name', '') or '未知告警'  # 移除长度限制
                me_name = alarm.get('me_name', '') or '未知网元'      # 移除长度限制
                ne_ip = alarm.get('ne_ip', '') or '未知IP'
                severity = alarm.get('perceived_severity_name', '') or '未知'

                # 计算持续时间
                effective_minutes = alarm.get('_effective_minutes', 0)
                if effective_minutes >= 1440:  # 超过1天
                    duration_text = f"{effective_minutes//1440}天{(effective_minutes%1440)//60}小时"
                elif effective_minutes >= 60:  # 超过1小时
                    duration_text = f"{effective_minutes//60}小时{effective_minutes%60}分钟"
                else:
                    duration_text = f"{effective_minutes}分钟"

                alarm_text = f"{i}. {code_name} | {me_name} | {ne_ip} | {severity} | 持续{duration_text}"
                draw.text((config.MARGIN + 30, current_y), alarm_text,
                         fill=sustained_colors['root'], font=fonts['small'])
                current_y += 22

            # 移除省略信息显示逻辑，显示完整列表

            current_y += config.GROUP_SPACING

        # 绘制底部信息
        current_y += 20
        draw.line([(config.MARGIN, current_y), (sustained_width - config.MARGIN, current_y)],
                 fill=sustained_colors['header'], width=1)
        current_y += 15

        footer_text = "请及时处理相关告警"
        draw.text((config.MARGIN, current_y), footer_text,
                 fill=sustained_colors['text'], font=fonts['small'])

        # 保存到内存 - 不再需要裁剪
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG', quality=95)
        img_buffer.seek(0)

        return img_buffer

    # 集中列定义：后期增删列只需要改这里
    SELECTED_COLUMNS = ['状态','关联标记','告警名称','网元名称','发生时间','考核持续时间(小时)','位置信息','产品资源类型','RAN对象类型','RAN板类型']

    def build_alarm_row(self, alarm, status_label):
        """构建单条告警的行数据，与SELECTED_COLUMNS对应"""
        raw = alarm.get('raw_data') or {}

        # 关联标记映射
        relationflag = alarm.get('relationflag', 0)
        if relationflag == 1:
            relation_mark = '根源'
        elif relationflag == 2:
            relation_mark = '衍生'
        elif relationflag == 3:
            relation_mark = '次根源'
        else:
            relation_mark = '独立'

        # 文本字段回退
        code_name = alarm.get('code_name') or raw.get('codename') or '-'
        me_name = alarm.get('me_name') or raw.get('mename') or '-'
        location = alarm.get('location_info') or alarm.get('position_name') or raw.get('positionname') or '-'

        # 发生时间（修正优先级：真实告警时间优先）
        # 优先级：原始告警时间 > 数据库存储时间 > 程序生成时间
        raw_time = (raw.get('alarmraisedtime')          # 1. 原始告警发生时间（最准确）
                    or alarm.get('alarm_raised_time')   # 2. 数据库存储的告警时间
                    or alarm.get('first_seen_at')       # 3. 程序首次发现时间（备用）
                    or alarm.get('last_seen_at')        # 4. 程序最后更新时间（最后备用）
                    or '-')

        # 使用统一的时间标准化函数
        alarm_time, alarm_time_text = self._normalize_alarm_time(raw_time)

        # 考核持续时间（分钟多级回退 → 小时）
        minutes = (alarm.get('_effective_minutes')
                   or alarm.get('effective_duration_minutes')
                   or self._get_effective_minutes_for_alarm(alarm)
                   or 0)
        try:
            duration_hours = round(float(minutes) / 60.0, 1) if float(minutes) > 0 else 0.0
        except Exception:
            duration_hours = 0.0

        # 额外三列（存在则写值，不存在写'-'）
        product_restype = alarm.get('raw_productrestype') or raw.get('productRestype') or '-'
        ran_object_type = alarm.get('raw_ran_fm_alarm_object_type') or raw.get('ran_fm_alarm_object_type') or '-'
        ran_board_type = alarm.get('raw_ran_fm_alarm_board_type') or raw.get('ran_fm_alarm_board_type') or '-'

        return {
            'excel_row': [status_label, relation_mark, code_name, me_name, alarm_time, duration_hours, location, product_restype, ran_object_type, ran_board_type],
            'text_row': [status_label, relation_mark, str(code_name), str(me_name), alarm_time_text, ("%.1f" % duration_hours), str(location), str(product_restype), str(ran_object_type), str(ran_board_type)]
        }

    def create_alarm_excel_DELETED(self, new_groups, sustained_groups=None):
        """创建告警Excel表格"""
        try:
            import pandas as pd
            from datetime import datetime

            all_rows = []

            # 处理新告警数据
            if new_groups:
                for group_key, alarms in new_groups.items():
                    for alarm in alarms:
                        # 关联标记映射
                        relationflag = alarm.get('relationflag', 0)
                        if relationflag == 1:
                            relation_mark = '根源'
                        elif relationflag == 2:
                            relation_mark = '衍生'
                        elif relationflag == 3:
                            relation_mark = '次根源'
                        else:
                            relation_mark = '独立'

                        # 发生时间（修正优先级：真实告警时间优先）
                        raw = alarm.get('raw_data') or {}
                        raw_time = (raw.get('alarmraisedtime')          # 1. 原始告警发生时间（最准确）
                                    or alarm.get('alarm_raised_time')   # 2. 数据库存储的告警时间
                                    or alarm.get('first_seen_at')       # 3. 程序首次发现时间（备用）
                                    or alarm.get('last_seen_at')        # 4. 程序最后更新时间（最后备用）
                                    or '-')

                        # 使用统一的时间标准化函数
                        alarm_time, _ = self._normalize_alarm_time(raw_time)

                        # 考核持续时间（分钟多级回退 → 小时）
                        minutes = (alarm.get('_effective_minutes')
                                   or alarm.get('effective_duration_minutes')
                                   or self._get_effective_minutes_for_alarm(alarm)
                                   or 0)
                        try:
                            duration_hours = round(float(minutes) / 60.0, 1) if float(minutes) > 0 else 0.0
                        except Exception:
                            duration_hours = 0.0

                        # 文本字段回退
                        code_name = alarm.get('code_name') or raw.get('codename') or '-'
                        me_name = alarm.get('me_name') or raw.get('mename') or '-'
                        location = alarm.get('location_info') or alarm.get('position_name') or raw.get('positionname') or '-'

                        row_data = self.build_alarm_row(alarm, '新告警')
                        all_rows.append(dict(zip(self.SELECTED_COLUMNS, row_data['excel_row'])))

            # 处理持续告警数据
            if sustained_groups:
                for _, alarms in sustained_groups.items():
                    for alarm in alarms:
                        row_data = self.build_alarm_row(alarm, '持续告警')
                        all_rows.append(dict(zip(self.SELECTED_COLUMNS, row_data['excel_row'])))

            if not all_rows:
                return None

            # 创建DataFrame
            df = pd.DataFrame(all_rows)

            # 规范数据类型：发生时间为datetime，持续时间为数值
            if '发生时间' in df.columns:
                try:
                    # 将占位符 '-' 视为缺失
                    df['发生时间'] = df['发生时间'].replace('-', pd.NaT)
                    df['发生时间'] = pd.to_datetime(df['发生时间'], errors='coerce')
                except Exception:
                    pass
            if '考核持续时间(小时)' in df.columns:
                try:
                    df['考核持续时间(小时)'] = pd.to_numeric(df['考核持续时间(小时)'], errors='coerce').fillna(0).round(1)
                except Exception:
                    pass

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'告警报告_{timestamp}.xlsx'

            # 保存到临时文件
            import tempfile
            import os
            temp_dir = tempfile.gettempdir()
            filepath = os.path.join(temp_dir, filename)

            # 写入Excel文件
            with pd.ExcelWriter(filepath, engine='openpyxl', datetime_format='yyyy-mm-dd hh:mm:ss') as writer:
                df.to_excel(writer, sheet_name='告警汇总', index=False)

                # 设置列宽与格式
                worksheet = writer.sheets['告警汇总']
                column_widths = {
                    'A': 12,  # 状态
                    'B': 10,  # 关联标记
                    'C': 40,  # 告警名称
                    'D': 28,  # 网元名称
                    'E': 20,  # 发生时间
                    'F': 18,  # 考核持续时间
                    'G': 28   # 位置信息
                }
                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width

                # 应用单元格格式（防止被识别为文本）
                try:
                    from openpyxl.styles import numbers
                    # 找到表头行
                    headers = [cell.value for cell in next(worksheet.iter_rows(min_row=1, max_row=1))]
                    col_idx_time = headers.index('发生时间') + 1 if '发生时间' in headers else None
                    col_idx_hours = headers.index('考核持续时间(小时)') + 1 if '考核持续时间(小时)' in headers else None
                    max_row = worksheet.max_row
                    if col_idx_time:
                        for r in range(2, max_row + 1):
                            c = worksheet.cell(row=r, column=col_idx_time)
                            c.number_format = 'yyyy-mm-dd hh:mm:ss'
                    if col_idx_hours:
                        for r in range(2, max_row + 1):
                            c = worksheet.cell(row=r, column=col_idx_hours)
                            c.number_format = '0.0'
                except Exception:
                    pass

            return filepath

        except ImportError:
            self.add_log("❌ 缺少pandas库，无法生成Excel文件。请安装: pip install pandas openpyxl")
            return None
        except Exception as e:
            self.add_log(f"❌ 生成Excel文件失败: {e}")
            return None

    def create_table_images_DELETED(self, headers, rows, rows_per_page=None):
        """用同一数据源渲染成一个或多个PNG表格图片 - 使用统一配置
        - headers: 列名数组，与 self.SELECTED_COLUMNS 一致
        - rows: 文本行数组（每行是列数组的字符串版本，已替换T为空格，不截断）
        - rows_per_page: 每页行数（不包含表头），None表示不分页显示所有数据
        """
        paths = []
        try:
            from PIL import Image, ImageDraw
        except Exception:
            self.add_log("⚠️ 未安装Pillow，跳过表格图片生成（仅发送Excel+正文）")
            return paths

        import tempfile, os, math, time

        # 使用统一的字体加载逻辑
        fonts = self._load_fonts()
        if not fonts:
            self.add_log("❌ 字体加载失败")
            return paths

        # 表格专用字体 - 稍大一些便于阅读
        table_font = fonts['text']  # 使用14号字体
        config = self.ImageConfig

        # 表格样式配置
        table_colors = {
            'header_bg': (245, 245, 245),
            'grid': config.COLORS['grid'],
            'text': (0, 0, 0),
            'bg': config.COLORS['bg'],
        }
        border = 1  # 网格线宽
        pad_x = 10
        pad_y = 6

        # 分页逻辑：如果rows_per_page为None，则不分页显示所有数据
        total_rows = len(rows)
        if rows_per_page is None:
            # 不分页，所有数据在一页显示
            pages = 1
            rows_per_page = total_rows
        else:
            # 原有分页逻辑
            pages = max(1, math.ceil(total_rows / max(1, rows_per_page)))

        # 计算列像素宽度（按每页分别计算，避免被极端长值撑爆所有页）
        def measure_col_widths(draw, font_to_use, data_rows):
            col_count = len(headers)
            widths = [0] * col_count
            # 先考虑表头
            for j in range(col_count):
                w = draw.textlength(str(headers[j]), font=font_to_use)
                widths[j] = max(widths[j], int(w) + pad_x * 2)
            # 再考虑数据
            for row in data_rows:
                for j in range(col_count):
                    w = draw.textlength(str(row[j]), font=font_to_use)
                    widths[j] = max(widths[j], int(w) + pad_x * 2)
            return widths

        # 行高
        def line_height(f):
            bbox = f.getbbox("Hg") if hasattr(f, 'getbbox') else (0, 0, 0, f.size)
            h = (bbox[3] - bbox[1]) if isinstance(bbox, tuple) else 16
            return h + pad_y * 2

        base_line_h = line_height(table_font)

        for p in range(pages):
            start = p * rows_per_page
            end = min(total_rows, (p + 1) * rows_per_page)
            page_rows = rows[start:end]

            # 先创建一个临时画布用于测量
            tmp_img = Image.new("RGB", (10, 10), table_colors['bg'])
            tmp_draw = ImageDraw.Draw(tmp_img)
            col_widths = measure_col_widths(tmp_draw, table_font, page_rows)
            table_width = sum(col_widths) + border * (len(headers) + 1)
            header_h = base_line_h
            data_h = base_line_h * len(page_rows)
            table_height = header_h + data_h + border * (len(page_rows) + 1)

            # 创建最终图片
            img = Image.new("RGB", (table_width, table_height), table_colors['bg'])
            draw = ImageDraw.Draw(img)

            # 网格与单元格
            col_x = [0]
            for w in col_widths:
                col_x.append(col_x[-1] + w + border)

            # 画表头背景和文字
            draw.rectangle([0, 0, table_width, header_h + border], fill=table_colors['header_bg'])
            for j, header in enumerate(headers):
                cell_x0 = col_x[j] + border
                cell_y0 = border
                draw.text((cell_x0 + pad_x, cell_y0 + pad_y), str(header),
                         font=table_font, fill=table_colors['text'])

            # 画表头底部边线
            draw.line([(0, header_h), (table_width, header_h)],
                     fill=table_colors['grid'], width=border)

            # 数据行
            cur_y = header_h + border
            for i, row in enumerate(page_rows):
                # 画行底边
                row_y1 = cur_y + base_line_h
                draw.line([(0, row_y1), (table_width, row_y1)],
                         fill=table_colors['grid'], width=border)
                # 画列竖线并写入文字
                for j, val in enumerate(row):
                    x0 = col_x[j] + border
                    draw.text((x0 + pad_x, cur_y + pad_y), str(val),
                             font=table_font, fill=table_colors['text'])
                cur_y = row_y1 + border

            # 画列竖线
            for cx in col_x:
                draw.line([(cx, 0), (cx, table_height)],
                         fill=table_colors['grid'], width=border)
            draw.line([(table_width - border, 0), (table_width - border, table_height)],
                     fill=table_colors['grid'], width=border)

            # 保存
            tmp_dir = tempfile.gettempdir()
            ts = time.strftime('%Y%m%d_%H%M%S')
            img_path = os.path.join(tmp_dir, f"AlarmReport_{ts}_p{p+1}.png")
            img.save(img_path, format='PNG')
            paths.append(img_path)

        return paths

    def create_test_email(self, test_number, total_tests):
        """创建测试邮件"""
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        current_time = datetime.now().strftime('%H:%M:%S')
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"[测试邮件{test_number}/{total_tests}] 告警邮件对比测试 {current_time}"

        test_content = f"""
告警邮件对比测试

测试编号: {test_number}/{total_tests}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试目的: 对比告警邮件和测试邮件的到达时间差异

如果您收到这封测试邮件，请对比：
1. 测试邮件的到达时间
2. 同批次告警邮件的到达时间
3. 观察是否有延迟差异

告警监控系统 - 邮件到达测试
"""
        msg.attach(MIMEText(test_content, 'plain', 'utf-8'))
        return msg

    def send_emails_batch(self, settings, email_list):
        """批量发送邮件 - 每封邮件独立连接，避免连接复用问题"""
        import smtplib
        import time

        # 初始化批次发送计数器
        current_batch_size = random.randint(2, 3)  # 随机批次大小2-3封
        batch_count = 0

        if not email_list:
            return []

        results = []
        batch_start_time = datetime.now()
        self.add_log(f"📧 开始发送 {len(email_list)} 封邮件")

        for i, (msg, email_info) in enumerate(email_list, 1):
            server = None
            try:
                send_start = datetime.now()

                # 每封邮件独立连接
                if settings['use_ssl']:
                    server = smtplib.SMTP_SSL(settings['smtp_host'], settings['smtp_port'], timeout=15)
                else:
                    server = smtplib.SMTP(settings['smtp_host'], settings['smtp_port'], timeout=15)

                server.login(settings['username'], settings['password'])

                # 发送邮件
                result = server.sendmail(settings['from_addr'], [settings['to_addr']], msg.as_string())

                server.quit()
                send_duration = (datetime.now() - send_start).total_seconds()

                if result:
                    self.add_log(f"📧 [{i}/{len(email_list)}] 发送完成但有警告 ({send_duration:.1f}秒)")
                    results.append((False, email_info, f"警告: {result}"))
                else:
                    self.add_log(f"📧 [{i}/{len(email_list)}] 发送成功 ({send_duration:.1f}秒)")
                    results.append((True, email_info, "成功"))

                # 批量发送间隔策略：发2-3封停3-8秒
                if i < len(email_list):
                    batch_count += 1

                    # 检查是否完成一个批次
                    if batch_count >= current_batch_size:
                        # 批次完成，长停顿
                        long_interval = random.uniform(3.0, 8.0)  # 随机3-8秒停顿
                        self.add_log(f"📧 批次发送完成({batch_count}封)，停顿{long_interval:.1f}秒")
                        time.sleep(long_interval)
                        # 重置批次计数，准备下一批次
                        current_batch_size = random.randint(2, 3)
                        batch_count = 0
                    else:
                        # 批次内短间隔
                        short_interval = random.uniform(0.3, 0.8)  # 随机0.3-0.8秒间隔
                        time.sleep(short_interval)

            except Exception as e:
                self.add_log(f"❌ [{i}/{len(email_list)}] 发送失败: {e}")
                results.append((False, email_info, str(e)))
            finally:
                if server:
                    try:
                        server.quit()
                    except Exception:
                        pass

        batch_duration = (datetime.now() - batch_start_time).total_seconds()
        success_count = sum(1 for success, _, _ in results if success)
        self.add_log(f"📧 发送完成: {success_count}/{len(email_list)} 成功，耗时{batch_duration:.1f}秒")

        return results

    def maybe_send_email(self, alarms):
        import time
        start_time = time.time()

        # 检查EmailManager是否已初始化
        if not self.email_manager:
            self.add_log("📧 邮件系统未就绪，跳过发送")
            return

        # 使用EmailManager统一检查邮件状态
        if not self.email_manager.is_enabled():
            self.add_log("📧 邮件功能已关闭")
            return

        # 验证邮件配置
        is_valid, message = self.email_manager.validate_settings()
        if not is_valid:
            self.add_log(f"📧 邮件配置有误: {message}")
            return

        # 获取邮件设置
        settings = self.email_manager.get_settings()

        # 显示邮件功能状态
        encryption_enabled = self.email_manager.is_encryption_enabled()
        image_enabled = settings.get('enable_image_attachment', False)
        self.add_log(f"📧 邮件功能状态: 加密={encryption_enabled}, 图片附件={image_enabled}")

        candidates = self.build_email_candidates(alarms)

        if not candidates:
            self.add_log(f"📧 无新告警需要发送")
            return

        # 按短ID分组（用于邮件内容分类显示）
        groups = {}
        for a in candidates:
            gk = self._group_key_short(a)
            groups.setdefault(gk, []).append(a)

        # 验证分组逻辑（调试模式）
        if len(groups) > 0:
            self._validate_grouping_logic(groups)

        # 检查邮件大小风险
        is_high_risk, estimated_size = self._check_email_size_risk(groups)
        if is_high_risk:
            self.add_log(f"⚠️ 邮件大小风险警告: 预估{estimated_size:.1f}MB，可能被邮件系统拒收")
            self.add_log(f"💡 建议: 考虑启用告警数量限制或联系邮件管理员调整限制")

        if not groups:
            return

        # 🔍 调试42d731分组问题 - 邮件发送前数据检查
        for group_key, alarms in groups.items():
            if '42d731' in group_key:
                self.add_log(f"🔍 邮件发送调试 - 分组: {group_key}")
                for alarm in alarms:
                    self.add_log(f"  告警: {alarm.get('code_name', '未知')}")
                    self.add_log(f"  root_group_id: '{alarm.get('root_group_id', '')}'")
                    self.add_log(f"  root_group_id_short: '{alarm.get('root_group_id_short', '')}'")
                    self.add_log(f"  alarm_key: {alarm.get('alarm_key', '')}")
                    self.add_log(f"  relationflag: {alarm.get('relationflag', 0)}")

        # 🔍 检查是否有其他告警应该与42d731分组
        target_root_id = '42d73118-3c1f-4179-aa93-2e3fe4620bd9_7d26e6b1d1cf231abe387575344ac4b3'
        related_alarms = []
        for group_key, alarms in groups.items():
            for alarm in alarms:
                if alarm.get('root_group_id', '') == target_root_id and alarm.get('alarm_key', '') != target_root_id:
                    related_alarms.append((group_key, alarm))

        if related_alarms:
            self.add_log(f"🔍 发现与42d731相关的告警被分到其他组:")
            for group_key, alarm in related_alarms:
                self.add_log(f"  分组: {group_key}, 告警: {alarm.get('code_name', '未知')}")
        else:
            self.add_log(f"🔍 没有发现其他告警与42d731有相同的root_group_id")

        # 使用拆分发送模式（按组发送）
        self.add_log(f"📧 检测到 {len(candidates)} 条新告警，分为 {len(groups)} 组，将分别发送独立邮件")
        self.send_individual_emails(groups, settings)

    def send_individual_emails(self, groups, settings):
        """拆分发送：每组告警发送一封独立邮件 - Linus式简单直接"""
        import smtplib
        import time
        from datetime import datetime

        total_groups = len(groups)
        success_count = 0
        failed_count = 0

        self.add_log(f"📧 开始分别发送 {total_groups} 组告警邮件...")

        for group_index, (group_key, group_alarms) in enumerate(groups.items(), 1):
            try:
                # 为每组创建独立的邮件
                msg = self.compose_single_group_email(group_key, group_alarms)

                # 设置邮件头
                from_addr = settings['from_addr'].strip()
                to_addr = settings['to_addr'].strip()

                # 验证邮箱格式
                if '@' not in from_addr or '@' not in to_addr:
                    self.add_log(f"❌ 邮箱格式错误: 发件人={from_addr}, 收件人={to_addr}")
                    failed_count += 1
                    continue

                msg['From'] = from_addr
                msg['To'] = to_addr

                # 发送邮件
                self.add_log(f"📧 正在发送第 {group_index}/{total_groups} 组告警邮件 (根源ID: {group_key[:8]}...)")

                server = None
                try:
                    send_start = datetime.now()

                    # 建立连接
                    if settings['use_ssl']:
                        server = smtplib.SMTP_SSL(settings['smtp_host'], settings['smtp_port'], timeout=15)
                    else:
                        server = smtplib.SMTP(settings['smtp_host'], settings['smtp_port'], timeout=15)

                    server.login(settings['username'], settings['password'])

                    # 发送邮件
                    result = server.sendmail(settings['from_addr'], [settings['to_addr']], msg.as_string())
                    server.quit()

                    send_duration = (datetime.now() - send_start).total_seconds()

                    if result:
                        self.add_log(f"📧 第 {group_index} 组邮件发送完成但有警告 ({send_duration:.1f}秒): {result}")
                    else:
                        self.add_log(f"✅ 第 {group_index} 组邮件发送成功 ({send_duration:.1f}秒) - {len(group_alarms)} 条告警")

                    # 标记该组中的告警为已发送
                    for a in group_alarms:
                        key = a.get('alarm_key') or a.get('raw_data', {}).get('alarmkey')
                        if key:
                            self.sent_email_keys.add(key)

                    success_count += 1

                    # 发送间隔：避免邮件服务器限制
                    if group_index < total_groups:  # 最后一封不需要等待
                        time.sleep(0.1)  # 0.1秒间隔，快速发送

                except Exception as e:
                    self.add_log(f"❌ 第 {group_index} 组邮件发送失败: {e}")
                    failed_count += 1
                finally:
                    if server:
                        try:
                            server.quit()
                        except Exception:
                            pass

            except Exception as e:
                self.add_log(f"❌ 第 {group_index} 组邮件生成失败: {e}")
                failed_count += 1

        # 发送完成统计
        self.add_log(f"📧 邮件发送完成: 成功 {success_count} 组, 失败 {failed_count} 组")

    def compose_single_group_email(self, group_key, group_alarms):
        """为单个告警组创建邮件内容 - 简化版本"""
        from email.mime.multipart import MIMEMultipart
        from email.mime.text import MIMEText
        from datetime import datetime
        import uuid
        from email.utils import formatdate, make_msgid

        # 创建邮件对象
        msg = MIMEMultipart('alternative')

        # 设置邮件头
        alarm_count = len(group_alarms)
        first_alarm = group_alarms[0] if group_alarms else {}
        alarm_name = first_alarm.get('code_name', '未知告警')[:50]

        # 邮件主题
        subject = f"[告警通知] {alarm_name} 等 {alarm_count} 条告警 - {datetime.now().strftime('%m-%d %H:%M')}"
        msg['Subject'] = subject
        msg['Date'] = formatdate(localtime=True)
        msg['Message-ID'] = make_msgid(domain='alarm-monitor.local')
        msg['X-Mailer'] = 'AlarmMonitor/2.0'
        msg['X-Priority'] = '2'  # 高优先级
        msg['Importance'] = 'High'

        # 添加UUID随机注释降低内容相似度
        random_uuid = str(uuid.uuid4())

        # 生成邮件正文
        content = self.generate_group_email_content(group_key, group_alarms, random_uuid)

        # 检查是否需要加密
        final_content = content  # 默认使用原始内容
        if self.email_manager and self.email_manager.is_encryption_enabled():
            encrypted_content, is_encrypted = self.email_manager.encrypt_email_content(content)
            if is_encrypted:
                # 加密成功，构造加密邮件
                encryption_settings = self.email_manager.get_encryption_settings()
                if encryption_settings.get('include_decrypt_info', True):
                    decrypt_info = EmailEncryption.create_decrypt_instructions(
                        encryption_settings.get('encryption_password', '')
                    )
                    final_content = f"{decrypt_info}\n\n-----BEGIN ENCRYPTED CONTENT-----\n{encrypted_content}\n-----END ENCRYPTED CONTENT-----"
                else:
                    final_content = f"-----BEGIN ENCRYPTED CONTENT-----\n{encrypted_content}\n-----END ENCRYPTED CONTENT-----"

                self.add_log(f"🔐 邮件内容已加密")
            else:
                # 加密失败，使用明文
                final_content = content

        # 添加邮件正文
        msg.attach(MIMEText(final_content, 'plain', 'utf-8'))

        # 🖼️ 可选功能：生成图片附件（始终使用明文内容，便于直接查看）
        if self.email_manager and hasattr(self.email_manager, 'get_settings'):
            settings = self.email_manager.get_settings()
            image_enabled = settings.get('enable_image_attachment', False)

            if image_enabled:
                self.add_log("🖼️ 图片附件功能已启用，开始生成图片...")
                try:
                    # 图片始终使用明文内容，即使邮件正文加密了也能直接查看图片
                    img_buffer = self.EmailImageGenerator.create_text_image(content)
                    if img_buffer:
                        from email.mime.image import MIMEImage
                        from datetime import datetime

                        img_data = img_buffer.getvalue()
                        img_attachment = MIMEImage(img_data)
                        filename = f"alarm_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                        img_attachment.add_header('Content-Disposition', f'attachment; filename="{filename}"')
                        msg.attach(img_attachment)

                        # 计算文本行数
                        text_lines = len(content.split('\n'))
                        self.add_log(f"📸 图片附件生成成功: {filename}")
                        self.add_log(f"📊 图片信息: {len(img_data)} 字节, {text_lines} 行文本, 明文显示")
                    else:
                        self.add_log("⚠️ 图片生成失败（可能缺少PIL库）")
                        self.add_log("💡 提示: 请安装Pillow库: pip install Pillow")
                except Exception as e:
                    self.add_log(f"❌ 图片附件生成失败: {e}")
                    self.add_log("💡 建议: 检查PIL库是否正确安装")
            else:
                self.add_log("🖼️ 图片附件功能已关闭，跳过图片生成")
        else:
            self.add_log("⚠️ 邮件管理器未就绪，无法检查图片附件设置")

        return msg

    def generate_group_email_content(self, group_key, group_alarms, random_uuid):
        """生成单组告警的邮件内容"""
        from datetime import datetime

        content_lines = []
        # 根据分组类型确定邮件标题
        if group_key.startswith('独立_'):
            title = "告警监控系统 - 独立告警通知"
        else:
            title = "告警监控系统 - 新告警通知"
        content_lines.append(title)
        content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_lines.append(f"根源分组ID: {group_key}")
        content_lines.append(f"告警数量: {len(group_alarms)} 条")
        content_lines.append("")
        content_lines.append("=" * 60)
        content_lines.append("")

        # 添加告警详情
        for i, alarm in enumerate(group_alarms, 1):
            content_lines.append(f"告警 {i}:")

            # 告警名称 - 确保不为空
            code_name = alarm.get('code_name', '') or '未知告警'
            if code_name and code_name != '未知':
                content_lines.append(f"  告警名称: {code_name}")

            # 位置信息（优先级：position_name > raw_data.positionname > 未知位置）
            position_info = (alarm.get('position_name')
                           or alarm.get('raw_data', {}).get('positionname')
                           or '未知位置')
            if position_info and position_info != '未知':
                # 翻译位置信息
                translated_position = self.PositionTranslator.safe_translate_position(position_info)
                content_lines.append(f"  位置信息: {translated_position}")

            # 网元名称 - 确保不为空
            me_name = alarm.get('me_name', '') or '未知网元'
            if me_name and me_name != '未知':
                content_lines.append(f"  网元名称: {me_name}")

            # 发生时间 - 使用多种时间字段
            alarm_time = None
            # 优先使用已格式化的时间字符串
            if alarm.get('time_str') and alarm.get('time_str') != '未知':
                alarm_time = alarm.get('time_str')
                # 如果是短格式(MM-DD HH:MM)，尝试补全年份
                if len(alarm_time) == 11 and '-' in alarm_time and ':' in alarm_time:
                    from datetime import datetime
                    current_year = datetime.now().year
                    alarm_time = f"{current_year}-{alarm_time}"
            else:
                # 尝试从原始时间戳转换
                raw_time = alarm.get('alarm_raised_time')
                if raw_time and raw_time != '未知':
                    try:
                        if isinstance(raw_time, (int, float)):
                            timestamp = raw_time
                            if timestamp > 1e12:  # 毫秒时间戳
                                timestamp = timestamp / 1000
                            from datetime import datetime
                            alarm_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        elif isinstance(raw_time, str) and raw_time.isdigit():
                            timestamp = float(raw_time)
                            if timestamp > 1e12:  # 毫秒时间戳
                                timestamp = timestamp / 1000
                            from datetime import datetime
                            alarm_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

            if alarm_time:
                content_lines.append(f"  发生时间: {alarm_time}")

            # 持续时间信息
            duration_text = None
            # 优先使用已格式化的持续时间
            if alarm.get('duration_str') and alarm.get('duration_str') != '未知':
                duration_text = alarm.get('duration_str')
            else:
                # 使用数值计算
                duration_minutes = alarm.get('effective_duration_minutes', 0)
                if duration_minutes and duration_minutes > 0:
                    duration_text = format_duration_text(duration_minutes)

            if duration_text:
                content_lines.append(f"  持续时间: {duration_text}")

            # 关联标记
            relation_marks = alarm.get('relation_marks', '')
            if relation_marks:
                content_lines.append(f"  关联标记: {relation_marks}")

            # 产品资源类型
            product_res_type = alarm.get('raw_productrestype', '') or alarm.get('raw_productrestype_value', '')
            if product_res_type and product_res_type != '未知':
                content_lines.append(f"  产品资源类型: {product_res_type}")

            # NBI ID
            nbi_id = alarm.get('nbi_id', '') or alarm.get('raw_data', {}).get('nbiId', '')
            if nbi_id and nbi_id != '未知':
                content_lines.append(f"  NBI ID: {nbi_id}")

            content_lines.append("")

        content_lines.append("=" * 60)
        content_lines.append("")
        content_lines.append("请及时处理相关告警。")
        content_lines.append("")
        content_lines.append(f"<!-- UUID: {random_uuid} -->")

        return "\n".join(content_lines)





    def _ensure_email_state_table(self):
        try:
            conn = sqlite3.connect(self.db_file)
            cur = conn.cursor()
            cur.execute(
                """
                CREATE TABLE IF NOT EXISTS email_state_groups (
                    group_key TEXT PRIMARY KEY,
                    last_threshold_sent INTEGER NOT NULL DEFAULT 0,
                    last_sent_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_subject TEXT
                )
                """
            )
            # 兼容性处理：如果存在旧的root_id列，迁移数据
            try:
                cur.execute("SELECT name FROM pragma_table_info('email_state_groups') WHERE name='root_id'")
                if cur.fetchone():
                    cur.execute("ALTER TABLE email_state_groups RENAME COLUMN root_id TO group_key")
                    self.add_log("📧 数据库表结构已升级：root_id → group_key")
            except Exception:
                pass
            conn.commit()
        except Exception as e:
            self.add_log(f"📧 初始化email_state_groups表失败: {e}")
        finally:
            try:
                conn.close()
            except Exception:
                pass





    def _get_group_last_threshold(self, group_key):
        try:
            conn = sqlite3.connect(self.db_file)
            cur = conn.cursor()
            cur.execute("SELECT last_threshold_sent FROM email_state_groups WHERE group_key=?", (group_key,))
            row = cur.fetchone()
            return int(row[0]) if row and row[0] is not None else 0
        except Exception as e:
            self.add_log(f"📧 读取阈值失败({group_key}): {e}")
            return 0
        finally:
            try:
                conn.close()
            except Exception:
                pass

    def _parse_duration_to_minutes(self, val):
        """将多种格式的持续时间转为分钟（支持数字/中文时长）。失败返回0。"""
        if val is None:
            return 0
        # 数字直接返回
        if isinstance(val, (int, float)):
            try:
                v = int(val)
                return max(v, 0)
            except Exception:
                return 0
        # 字符串处理
        s = str(val).strip()
        if not s:
            return 0
        # 纯数字或小数
        try:
            v = int(float(s))
            return max(v, 0)
        except Exception:
            pass
        # 中文时长解析：1天3小时5分钟、10小时12分、75分钟、1天、2天4小时
        import re
        days = hours = minutes = 0
        m = re.search(r"([0-9]+)\s*天", s)
        if m:
            days = int(m.group(1))
        m = re.search(r"([0-9]+)\s*(小时|时)", s)
        if m:
            hours = int(m.group(1))
        m = re.search(r"([0-9]+)\s*(分钟|分)", s)
        if m:
            minutes = int(m.group(1))
        total = days * 1440 + hours * 60 + minutes
        return max(total, 0)

    def _calc_effective_minutes_fallback(self, alarm):
        """当分钟字段不可用时，基于起始时间用考核规则计算分钟数。"""
        from datetime import datetime
        # 获取起始时间戳（秒/毫秒）
        ts = alarm.get('alarm_raised_time')
        if ts is None:
            raw = alarm.get('raw_data', {})
            ts = raw.get('alarmraisedtime') if isinstance(raw, dict) else None
        # 解析成秒
        start_dt = None
        try:
            if ts is None:
                return 0
            if isinstance(ts, str):
                ts = float(ts)
            # 判断单位
            if ts > 1e12:
                ts = ts / 1000.0  # 毫秒
            start_dt = datetime.fromtimestamp(ts)
        except Exception:
            return 0
        try:
            return calculate_effective_duration(start_dt, datetime.now())
        except Exception:
            # 兜底：自然时间差分钟
            try:
                delta = datetime.now() - start_dt
                return max(int(delta.total_seconds() // 60), 0)
            except Exception:
                return 0

    def _is_alarm_too_old(self, a):
        """快速检查告警是否超过5天（7200分钟），避免复杂的考核时间计算"""
        from datetime import datetime, timedelta

        # 先检查数据库字段
        dur = a.get('effective_duration_minutes')
        if dur is not None:
            minutes = self._parse_duration_to_minutes(dur)
            if minutes > 0:
                return minutes >= 7200

        # 快速时间检查：简单计算自然时间差
        ts = a.get('alarm_raised_time')
        if ts is None:
            raw = a.get('raw_data', {})
            ts = raw.get('alarmraisedtime') if isinstance(raw, dict) else None

        if ts is None:
            return False  # 无法判断时间，不过滤

        try:
            if isinstance(ts, str):
                ts = float(ts)
            # 判断单位
            if ts > 1e12:
                ts = ts / 1000.0  # 毫秒转秒
            start_dt = datetime.fromtimestamp(ts)

            # 简单检查：如果自然时间差超过6天，肯定超过5天考核时间
            time_diff = datetime.now() - start_dt
            if time_diff > timedelta(days=6):
                return True

            # 如果自然时间差小于4天，肯定不超过5天考核时间
            if time_diff < timedelta(days=4):
                return False

            # 4-6天之间需要精确计算，但为了性能，我们保守处理
            return False  # 不过滤，让邮件发送逻辑处理

        except Exception:
            return False  # 解析失败，不过滤

    def _get_effective_minutes_for_alarm(self, a):
        """优先用字段值（含中文解析），失败则用兜底计算。"""
        dur = a.get('effective_duration_minutes')
        minutes = self._parse_duration_to_minutes(dur)
        if minutes <= 0:
            minutes = self._calc_effective_minutes_fallback(a)
        return minutes

    def _set_group_last_threshold(self, group_key, threshold, subject):
        try:
            conn = sqlite3.connect(self.db_file)
            cur = conn.cursor()
            cur.execute(
                """
                INSERT INTO email_state_groups(group_key, last_threshold_sent, last_sent_at, last_subject)
                VALUES(?, ?, CURRENT_TIMESTAMP, ?)
                ON CONFLICT(group_key) DO UPDATE SET
                    last_threshold_sent=excluded.last_threshold_sent,
                    last_sent_at=CURRENT_TIMESTAMP,
                    last_subject=excluded.last_subject
                """, (group_key, int(threshold), subject)
            )
            conn.commit()
        except Exception as e:
            self.add_log(f"📧 更新阈值失败({group_key}): {e}")
        finally:
            try:
                conn.close()
            except Exception:
                pass

    def maybe_send_sustained_emails(self, alarms):
        """持续阈值类邮件：按根源ID分组，本轮触发最高阈值的一封（持久化去重）"""
        # 检查EmailManager是否已初始化
        if not self.email_manager:
            self.add_log("📧 邮件系统未就绪，跳过持续告警邮件")
            return

        # 使用EmailManager统一检查邮件状态
        if not self.email_manager.is_enabled():
            self.add_log("📧 邮件功能已关闭")
            return

        # 验证邮件配置
        is_valid, message = self.email_manager.validate_settings()
        if not is_valid:
            self.add_log(f"📧 邮件配置有误: {message}")
            return

        self._ensure_email_state_table()

        # 创建时间计算缓存，避免重复计算
        time_cache = {}
        def get_cached_minutes(a):
            alarm_key = a.get('alarm_key') or a.get('raw_data', {}).get('alarmkey', '')
            if alarm_key in time_cache:
                return time_cache[alarm_key]
            minutes = self._get_effective_minutes_for_alarm(a)
            time_cache[alarm_key] = minutes
            return minutes

        # 预检统计（展示用小时）
        total = len(alarms)
        sustained_total = sum(1 for a in alarms if a.get('is_active', 1) == 1 and a.get('is_new', 0) == 0)
        sustained_1h = 0
        sustained_1h_unicom_focus_rel = 0
        sample_info = None

        # 只对持续告警进行时间计算
        for a in alarms:
            if not (a.get('is_active', 1) == 1 and a.get('is_new', 0) == 0):
                continue
            is_unicom = '联通' in str(a.get('operator_info', ''))
            is_focus = any(kw in (a.get('code_name', '') or '') for kw in self.focus_keywords)
            is_related = a.get('relationflag', 0) in (1, 2, 3)

            # 只对符合条件的告警计算时间
            if is_unicom and (is_focus or is_related):
                minutes = get_cached_minutes(a)
                if minutes >= 60:
                    sustained_1h += 1
                    sustained_1h_unicom_focus_rel += 1
                    if sample_info is None:
                        sample_info = f"样本: 名称={a.get('code_name','')} 运营商={a.get('operator_info','')} 持续={minutes//60}h{minutes%60 if minutes%60 else ''}m 关联flag={a.get('relationflag',0)}"

        self.add_log(f"📧 持续告警检查: 总数{total}条，超1小时{sustained_1h}条，符合条件{sustained_1h_unicom_focus_rel}条")

        # 先筛选满足持续条件的
        sustained = []
        for a in alarms:
            if not (a.get('is_active', 1) == 1 and a.get('is_new', 0) == 0):
                continue  # 非持续
            is_unicom = '联通' in str(a.get('operator_info', ''))
            is_focus = any(kw in (a.get('code_name', '') or '') for kw in self.focus_keywords)
            is_related = a.get('relationflag', 0) in (1, 2, 3)
            if not (is_unicom and (is_focus or is_related)):
                continue
            # 使用缓存的时间计算
            minutes = get_cached_minutes(a)
            # 找出当前跨越的最高阈值
            crossed = [t for t in SUSTAINED_THRESHOLDS_MINUTES if minutes >= t]
            if not crossed:
                continue
            a['_sustained_threshold'] = max(crossed)
            a['_effective_minutes'] = minutes
            sustained.append(a)

        if not sustained:
            self.add_log("📧 sustained候选=0（持续阈值·联通·重点/关联）")
            if sustained_1h_unicom_focus_rel > 0:
                # 打印前3个样本，帮助定位为何未入候选（大概率是持续分钟=0或类型不对）
                printed = 0
                for a in alarms:
                    if printed >= 3:
                        break
                    if not (a.get('is_active', 1) == 1 and a.get('is_new', 0) == 0):
                        continue
                    dur0 = a.get('effective_duration_minutes')
                    try:
                        dur_int = int(float(dur0)) if dur0 is not None else 0
                    except Exception:
                        dur_int = 0
                    if dur_int < 60:
                        continue
                    is_unicom = '联通' in str(a.get('operator_info', ''))
                    is_focus = any(kw in (a.get('code_name', '') or '') for kw in self.focus_keywords)
                    is_related = a.get('relationflag', 0) in (1, 2, 3)
                    if not (is_unicom and (is_focus or is_related)):
                        continue
                    printed += 1
                    self.add_log(
                        f"📧 sustained样本#{printed}: 名称={a.get('code_name','')} 持续raw={dur0} 类型={type(dur0)} "
                        f"解析后={dur_int}min 运营商={a.get('operator_info','')} 关联flag={a.get('relationflag',0)} 起始={a.get('time_str','')}"
                    )
            return

        # 按短ID分组（预览+发送都按短ID聚合）
        groups = {}
        for a in sustained:
            gk = self._group_key_short(a)
            groups.setdefault(gk, []).append(a)

        self.add_log(f"📧 检测到 {len(sustained)} 条持续告警，分为 {len(groups)} 组")

        # 合并所有持续告警到一封邮件
        # 邮件发送统计
        total_groups = len(groups)

        # 准备合并发送的数据结构：{group_key: (group_items, max_threshold)}
        groups_with_thresholds = {}

        for group_key, group_items in groups.items():

            # 规则：超过5天（7200分钟）的告警不发送（整组忽略）
            if any((x.get('_effective_minutes') or self._get_effective_minutes_for_alarm(x)) >= 7200 for x in group_items):
                continue

            # 筛选出需要发送的告警（按个体判断）
            items_to_send = []
            group_highest_threshold = 0

            for item in group_items:
                item_threshold = item.get('_sustained_threshold', 0)
                if item_threshold <= 0:
                    continue

                # 使用精确的持续阈值判断键
                threshold_key = self._sustained_threshold_key(item)
                last_sent = self._get_group_last_threshold(threshold_key)

                if item_threshold > last_sent:
                    # 该告警的阈值超过了历史最高，需要发送
                    items_to_send.append((item, threshold_key, item_threshold))
                    group_highest_threshold = max(group_highest_threshold, item_threshold)

            # 如果该组有需要发送的告警，加入合并列表
            if items_to_send:
                groups_with_thresholds[group_key] = (items_to_send, group_highest_threshold)





        # 分组独立发送持续告警邮件
        if groups_with_thresholds:
            self.add_log(f"📧 检测到 {len(groups_with_thresholds)} 组持续告警，将分别发送独立邮件")

            sent_count = 0
            failed_count = 0

            for group_index, (group_key, (items_to_send, group_highest_threshold)) in enumerate(groups_with_thresholds.items()):
                try:
                    # 为每个分组生成独立的邮件
                    group_alarms = [item[0] for item in items_to_send]  # 提取告警对象

                    # 创建邮件
                    from email.mime.text import MIMEText
                    from email.mime.multipart import MIMEMultipart
                    from datetime import datetime

                    msg = MIMEMultipart('mixed')

                    # 获取该组的网元名称作为主题
                    first_alarm = group_alarms[0] if group_alarms else {}
                    me_name = first_alarm.get('me_name', '未知网元')

                    # 使用统一的主题格式
                    timestamp = self._format_email_timestamp()
                    safe_me_name = self._safe_subject_text(me_name, 20)
                    threshold_hours = group_highest_threshold // 60 if group_highest_threshold >= 60 else group_highest_threshold
                    threshold_unit = "h" if group_highest_threshold >= 60 else "m"
                    subject = f"持续{threshold_hours}{threshold_unit}-{safe_me_name}-{timestamp}"

                    # 设置邮件头
                    import uuid
                    from email.utils import formatdate, make_msgid

                    msg['Subject'] = subject
                    msg['Date'] = formatdate(localtime=True)
                    msg['Message-ID'] = make_msgid(domain='alarm-monitor.local')
                    msg['X-Mailer'] = 'AlarmMonitor/2.0'
                    msg['X-Priority'] = '1'  # 最高优先级（持续告警更紧急）
                    msg['Importance'] = 'High'
                    msg['Auto-Submitted'] = 'auto-generated'
                    msg['Precedence'] = 'bulk'
                    msg['List-Unsubscribe'] = '<mailto:<EMAIL>>'
                    msg['X-Auto-Response-Suppress'] = 'All'

                    # 设置邮件头（确保格式正确）
                    settings = self.email_manager.get_settings()
                    from_addr = settings['from_addr'].strip()
                    to_addr = settings['to_addr'].strip()
                    if '@' not in from_addr or '@' not in to_addr:
                        self.add_log(f"❌ 邮箱格式错误(持续): 发件人={from_addr}, 收件人={to_addr}")
                        failed_count += 1
                        continue
                    msg['From'] = from_addr
                    msg['To'] = to_addr

                    # 生成详细的文本内容
                    content_lines = []
                    content_lines.append(f"持续告警通知 - {me_name}")
                    content_lines.append("")
                    content_lines.append(f"发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    content_lines.append(f"分组ID: {group_key}")
                    content_lines.append(f"持续告警数量: {len(group_alarms)} 条")
                    content_lines.append(f"最高阈值: {threshold_hours}{threshold_unit}")
                    content_lines.append("")
                    content_lines.append("=" * 60)
                    content_lines.append("")

                    # 添加每个告警的详细信息
                    for i, alarm in enumerate(group_alarms, 1):
                        content_lines.append(f"告警 {i}:")
                        content_lines.append(f"  告警名称: {alarm.get('code_name', '未知告警')}")
                        content_lines.append(f"  网元名称: {alarm.get('me_name', '未知网元')}")
                        # 位置信息（带翻译）
                        position_info = alarm.get('position_name', '未知位置')
                        translated_position = self.PositionTranslator.safe_translate_position(position_info)
                        content_lines.append(f"  位置信息: {translated_position}")
                        content_lines.append(f"  发生时间: {alarm.get('time_str', '未知时间')}")

                        # 持续时间信息
                        effective_minutes = alarm.get('_effective_minutes', 0)
                        if effective_minutes >= 1440:  # 超过1天
                            duration_text = f"{effective_minutes//1440}天{(effective_minutes%1440)//60}小时"
                        elif effective_minutes >= 60:  # 超过1小时
                            duration_text = f"{effective_minutes//60}小时{effective_minutes%60}分钟"
                        else:
                            duration_text = f"{effective_minutes}分钟"

                        if effective_minutes > 0:
                            content_lines.append(f"  持续时间: {duration_text}")

                        # 关联标记
                        relation_marks = alarm.get('relation_marks', '')
                        if relation_marks:
                            content_lines.append(f"  关联标记: {relation_marks}")

                        # 产品资源类型
                        product_res_type = alarm.get('raw_productrestype', '') or alarm.get('raw_productrestype_value', '')
                        if product_res_type and product_res_type != '未知':
                            content_lines.append(f"  产品资源类型: {product_res_type}")

                        # NBI ID
                        nbi_id = alarm.get('nbi_id', '') or alarm.get('raw_data', {}).get('nbiId', '')
                        if nbi_id and nbi_id != '未知':
                            content_lines.append(f"  NBI ID: {nbi_id}")

                        content_lines.append("")

                    content_lines.append("=" * 60)
                    content_lines.append("")
                    content_lines.append("请及时处理相关告警。")

                    simple_text = "\n".join(content_lines)

                    random_uuid = str(uuid.uuid4())
                    enhanced_text = f"{simple_text}\n\n<!-- UUID: {random_uuid} -->"

                    # 🔐 Linus式加密处理 - 与其他邮件方法保持一致
                    final_content = enhanced_text
                    if self.email_manager and self.email_manager.is_encryption_enabled():
                        try:
                            encrypted_content, is_encrypted = self.email_manager.encrypt_email_content(enhanced_text)
                            if is_encrypted:
                                # 构建加密邮件格式
                                encryption_settings = self.email_manager.get_encryption_settings()
                                password = encryption_settings.get('encryption_password', '')

                                final_content = f"""
🔐 此邮件内容已加密

-----BEGIN ENCRYPTED CONTENT-----
{encrypted_content}
-----END ENCRYPTED CONTENT-----
"""

                                # 如果启用了解密说明，添加解密指导
                                if encryption_settings.get('include_decrypt_info', True):
                                    decrypt_info = EmailEncryption.create_decrypt_instructions(password)
                                    final_content += decrypt_info

                                self.add_log("🔐 持续告警邮件内容已加密")
                            else:
                                self.add_log("⚠️ 持续告警邮件加密失败，使用明文发送")
                        except Exception as e:
                            self.add_log(f"🔐 持续告警邮件加密处理失败: {e}")
                            # 加密失败时使用原内容
                    else:
                        self.add_log("📧 持续告警邮件内容使用明文发送")

                    msg.attach(MIMEText(final_content, 'plain', 'utf-8'))

                    # 🖼️ 可选功能：生成图片附件（始终使用明文内容，便于直接查看）
                    if self.email_manager and hasattr(self.email_manager, 'get_settings'):
                        email_settings = self.email_manager.get_settings()
                        image_enabled = email_settings.get('enable_image_attachment', False)

                        if image_enabled:
                            self.add_log("🖼️ 持续告警图片附件功能已启用，开始生成图片...")
                            try:
                                from email.mime.image import MIMEImage
                                from datetime import datetime

                                # 图片始终使用明文内容，即使邮件正文加密了也能直接查看图片
                                img_buffer = self.EmailImageGenerator.create_text_image(enhanced_text)
                                if img_buffer:
                                    img_data = img_buffer.getvalue()
                                    img_attachment = MIMEImage(img_data)
                                    filename = f"sustained_alarm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                                    img_attachment.add_header('Content-Disposition', f'attachment; filename="{filename}"')
                                    msg.attach(img_attachment)

                                    # 计算文本行数
                                    text_lines = len(enhanced_text.split('\n'))
                                    self.add_log(f"📸 持续告警图片附件生成成功: {filename}")
                                    self.add_log(f"📊 图片信息: {len(img_data)} 字节, {text_lines} 行文本, 明文显示")
                                else:
                                    self.add_log("⚠️ 持续告警图片生成失败（可能缺少PIL库）")
                                    self.add_log("💡 提示: 请安装Pillow库: pip install Pillow")
                            except Exception as e:
                                self.add_log(f"❌ 持续告警图片附件生成失败: {e}")
                                self.add_log("💡 建议: 检查PIL库是否正确安装")
                        else:
                            self.add_log("🖼️ 持续告警图片附件功能已关闭，跳过图片生成")

                    # 发送邮件
                    self.add_log(f"📧 正在发送分组 {group_key} 的持续告警邮件（{len(group_alarms)} 条告警）...")

                    import smtplib
                    server = None
                    try:
                        send_start = datetime.now()

                        # 建立连接
                        if settings['use_ssl']:
                            server = smtplib.SMTP_SSL(settings['smtp_host'], settings['smtp_port'], timeout=15)
                        else:
                            server = smtplib.SMTP(settings['smtp_host'], settings['smtp_port'], timeout=15)

                        server.login(settings['username'], settings['password'])

                        # 发送邮件
                        result = server.sendmail(settings['from_addr'], [settings['to_addr']], msg.as_string())
                        server.quit()

                        send_duration = (datetime.now() - send_start).total_seconds()

                        if result:
                            self.add_log(f"📧 分组 {group_key} 持续告警邮件发送完成但有警告 ({send_duration:.1f}秒): {result}")
                        else:
                            self.add_log(f"📧 分组 {group_key} 持续告警邮件发送成功 ({send_duration:.1f}秒)")

                            # 更新该分组所有发送告警的阈值记录
                            for item_data in items_to_send:
                                a, threshold_key, item_threshold = item_data
                                self._set_group_last_threshold(threshold_key, item_threshold, subject)

                            sent_count += 1

                    except Exception as e:
                        self.add_log(f"❌ 分组 {group_key} 持续告警邮件发送失败: {e}")
                        failed_count += 1
                    finally:
                        if server:
                            try:
                                server.quit()
                            except Exception:
                                pass

                    # 持续告警邮件发送间隔策略（避免邮件服务器限制）
                    if group_index < len(groups_with_thresholds) - 1:  # 不是最后一个分组
                        import random
                        import time
                        interval = random.uniform(1.0, 3.0)  # 随机1-3秒间隔
                        self.add_log(f"📧 持续告警邮件发送间隔: {interval:.1f}秒")
                        time.sleep(interval)






                except Exception as e:
                    self.add_log(f"❌ 分组 {group_key} 持续告警邮件处理失败: {e}")
                    failed_count += 1

            # 发送完成统计
            self.add_log(f"📧 持续告警邮件发送完成: 成功 {sent_count} 封，失败 {failed_count} 封")
        else:
            self.add_log("📧 无持续告警需要发送")



        # 更新监控统计
        current_time = datetime.now()
        running_time = int((current_time - self.start_time).total_seconds())
        hours = running_time // 3600
        minutes = (running_time % 3600) // 60
        seconds = running_time % 60

        if hours > 0:
            time_str = f"{hours}小时{minutes}分钟"
        elif minutes > 0:
            time_str = f"{minutes}分钟{seconds}秒"
        else:
            time_str = f"{seconds}秒"

        # 显示统计信息
        timer_status = ""
        if self.fetch_timer_enabled and self.fetch_timer.isActive():
            timer_status = f" | ⏰定时获取:{self.fetch_interval_minutes}分钟"

        self.monitor_stats_label.setText(f"系统统计: 刷新次数:{self.refresh_count} | 运行时间:{time_str}{timer_status}")

    # 实时监控相关方法已删除

    def add_log(self, message):
        """添加日志消息"""
        if not self.real_time_mode:
            return

        if self.monitor_enabled:
            # 暂停实时监控
            self.data_timer.stop()
            self.monitor_enabled = False
            self.monitor_btn.setText("▶️ 开始监控")
            self.monitor_status.setText("⏸️ 实时监控已暂停")
            self.monitor_status.setStyleSheet("color: red; padding: 5px;")
            self.status_bar.showMessage("实时监控已暂停")
            self.add_log("⏸️ 实时监控已暂停")
        else:
            # 开始实时监控
            self.data_timer.start(AUTO_REFRESH_INTERVAL)
            self.monitor_enabled = True
            self.monitor_btn.setText("⏸️ 暂停监控")
            self.monitor_status.setText("� 实时监控中")
            self.monitor_status.setStyleSheet("color: green; padding: 5px;")
            self.status_bar.showMessage(f"实时监控已恢复 - 每{AUTO_REFRESH_INTERVAL//1000}秒自动获取")
            self.add_log(f"▶️ 实时监控已恢复 - 每{AUTO_REFRESH_INTERVAL//1000}秒自动从网管获取数据")
            # 立即获取一次数据
            self.auto_fetch_data()

    def toggle_monitor_mode(self):
        """切换监控模式"""
        if self.real_time_mode:
            # 切换到按需模式
            self.real_time_mode = False
            self.monitor_mode_btn.setText("🔄 切换到实时监控")
            self.monitor_btn.setVisible(False)

            # 停止所有定时器
            self.data_timer.stop()
            self.local_timer.stop()
            self.monitor_enabled = False

            self.add_log("📋 切换到按需监控模式")
            self.add_log("💡 在此模式下，数据不会自动刷新")
            self.add_log("💡 点击'从网管获取'获取最新数据")


            # 更新监控状态显示
            self.monitor_status.setText("📋 按需模式")
            self.monitor_status.setStyleSheet("color: blue; padding: 5px;")

        else:
            # 切换到实时监控模式
            self.real_time_mode = True
            self.monitor_mode_btn.setText("📋 切换到按需模式")
            self.monitor_btn.setVisible(True)
            self.monitor_btn.setText("⏸️ 暂停监控")

            # 启动实时监控
            self.monitor_enabled = True
            self.data_timer.start(AUTO_REFRESH_INTERVAL)  # 每10秒从网管获取数据

            self.add_log("🔄 切换到实时监控模式")
            self.add_log(f"⚡ 将每{AUTO_REFRESH_INTERVAL//1000}秒自动从网管获取最新数据")
            self.add_log("⚠️ 注意: 此模式会频繁访问网管系统")

            # 更新监控状态显示
            self.monitor_status.setText("🔄 实时监控中")
            self.monitor_status.setStyleSheet("color: green; padding: 5px;")

            # 立即获取一次数据
            self.auto_fetch_data()

    def auto_fetch_data(self):
        """自动获取数据（仅在实时监控模式下调用）"""
        if not self.real_time_mode or not self.monitor_enabled:
            return

        # 检查是否有获取任务正在进行
        if (hasattr(self, 'fetch_worker') and
            self.fetch_worker is not None and
            self.fetch_worker.isRunning()):
            self.add_log("⚠️ 上次数据获取还未完成，跳过本次自动获取")
            return

        self.add_log("🔄 自动从网管获取最新数据...")
        self.fetch_from_web()

    def add_log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.appendPlainText(log_entry)

        # 自动滚动到底部
        if self.auto_scroll_cb.isChecked():
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.End)
            self.log_text.setTextCursor(cursor)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空")

    def on_error(self, error_msg):
        """处理错误"""
        self.add_log(f"❌ 错误: {error_msg}")
        self.status_bar.showMessage(f"错误: {error_msg}")

    def update_progress(self, value):
        """更新进度条"""
        if self.progress_bar.isVisible():
            self.progress_bar.setValue(value)

    def update_stats(self, stats):
        """更新统计信息"""
        # 显示完整统计信息，包括当前显示的数据量
        displayed_count = len(self.displayed_alarms) if hasattr(self, 'displayed_alarms') else len(self.alarms_data)
        total_count = len(self.all_alarms) if hasattr(self, 'all_alarms') else stats['total']

        stats_text = (
            f"📊 数据库总计: {stats['total']}条 | "
            f"� 当前显示: {displayed_count}/{total_count}条 | "
            f"�根源: {stats['root']}条 | "
            f"🟡衍生: {stats['derived']}条 | "
            f"🎯重点: {stats['focus']}条"
        )
        self.stats_label.setText(stats_text)

    def is_priority_alarm(self, alarm):
        """判断是否为优先告警"""
        # 1. 重点关键字告警
        code_name = alarm.get('code_name', '') or ''
        is_focus = any(kw in code_name for kw in self.focus_keywords)

        # 2. 关联告警（根源、次根源、衍生）
        relationflag = alarm.get('raw_data', {}).get('relationflag', 0)
        is_related = relationflag in [1, 2, 3]

        # 3. 新告警
        is_new = alarm.get('is_new', 0) == 1

        return is_focus or is_related or is_new

    def sort_alarms_by_priority(self, alarms):
        """按优先级排序告警：重点关联 > 新告警 > 其他"""
        def get_priority(alarm):
            # 优先级评分（越小越优先）
            score = 0

            # 1. 重点关键字告警（最高优先级）
            code_name = alarm.get('code_name', '') or ''
            is_focus = any(kw in code_name for kw in self.focus_keywords)
            if is_focus:
                score -= 1000

            # 2. 关联告警（根源、次根源、衍生）
            relationflag = alarm.get('raw_data', {}).get('relationflag', 0)
            if relationflag == 1:  # 根源告警
                score -= 800
            elif relationflag in [2, 3]:  # 衍生、次根源
                score -= 600

            # 3. 新告警
            if alarm.get('is_new', 0) == 1:
                score -= 200

            # 4. 按时间排序（新的优先）
            alarm_time = alarm.get('alarm_raised_time', 0)
            try:
                score += (2000000000000 - int(alarm_time)) / 1000000000  # 时间越新分数越小
            except:
                score += 1000  # 无效时间放到最后

            return score

        return sorted(alarms, key=get_priority)

    def sort_alarms_by_time(self, alarms):
        """按时间排序告警（传统模式）"""
        return sorted(alarms, key=lambda x: x.get('alarm_raised_time', 0), reverse=True)

    def update_table_with_smart_pagination(self, alarms, initial_size=1000):
        """智能分页：优先显示重点关联告警"""

        # 1. 按模式排序
        if self.smart_mode:
            sorted_alarms = self.sort_alarms_by_priority(alarms)
        else:
            sorted_alarms = self.sort_alarms_by_time(alarms)

        # 2. 统计优先告警数量
        priority_count = 0
        if self.smart_mode:
            for alarm in sorted_alarms:
                if self.is_priority_alarm(alarm):
                    priority_count += 1
                else:
                    break  # 遇到第一个非优先告警就停止计数

        # 3. 确定初始显示数量
        if self.smart_mode and priority_count > 0:
            # 智能模式：至少显示所有优先告警+200条普通告警
            initial_display = max(initial_size, priority_count + 200)
        else:
            # 时间模式：固定显示数量
            initial_display = initial_size

        initial_display = min(initial_display, len(sorted_alarms))  # 不超过总数

        # 4. 保存数据
        self.all_alarms = sorted_alarms
        self.displayed_alarms = sorted_alarms[:initial_display]
        self.current_display_count = initial_display
        self.priority_count = priority_count

        # 5. 更新界面
        self.update_table_display(self.displayed_alarms)
        self.update_pagination_info()

    def create_smart_pagination_ui(self):
        """创建智能分页界面"""
        layout = QHBoxLayout()

        # 显示信息标签
        self.page_info_label = QLabel("📊 加载中...")
        self.page_info_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")

        # 加载更多按钮
        self.load_more_btn = QPushButton("📥 加载更多")
        self.load_more_btn.clicked.connect(self.load_more_alarms)
        self.load_more_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # 显示全部按钮
        self.show_all_btn = QPushButton("📋 显示全部")
        self.show_all_btn.clicked.connect(self.show_all_alarms)
        self.show_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)

        # 模式切换按钮
        self.mode_toggle_btn = QPushButton("🔄 切换到时间排序")
        self.mode_toggle_btn.clicked.connect(self.toggle_display_mode)
        self.mode_toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)

        # 布局
        layout.addWidget(QLabel("📊"))
        layout.addWidget(self.page_info_label)
        layout.addStretch()
        layout.addWidget(self.load_more_btn)
        layout.addWidget(self.show_all_btn)
        layout.addWidget(self.mode_toggle_btn)

        return layout

    def update_pagination_info(self):
        """更新分页信息显示"""
        total = len(self.all_alarms)
        displayed = len(self.displayed_alarms)
        priority = self.priority_count

        if self.smart_mode:
            if priority > 0:
                info_text = f"智能排序：显示 {displayed}/{total} 条 (含 {priority} 条重点/关联)"
            else:
                info_text = f"智能排序：显示 {displayed}/{total} 条"
        else:
            info_text = f"时间排序：显示 {displayed}/{total} 条"

        self.page_info_label.setText(info_text)

        # 显示/隐藏"加载更多"按钮
        if displayed < total:
            self.load_more_btn.show()
            remaining = total - displayed
            self.load_more_btn.setText(f"📥 加载更多 ({remaining} 条剩余)")
        else:
            self.load_more_btn.hide()

    def load_more_alarms(self):
        """加载更多告警"""
        current_count = len(self.displayed_alarms)
        new_count = min(current_count + 1000, len(self.all_alarms))

        self.displayed_alarms = self.all_alarms[:new_count]
        self.current_display_count = new_count
        self.update_table_display(self.displayed_alarms)
        self.update_pagination_info()

    def show_all_alarms(self):
        """显示全部告警"""
        self.displayed_alarms = self.all_alarms[:]
        self.current_display_count = len(self.all_alarms)
        self.update_table_display(self.displayed_alarms)
        self.update_pagination_info()

    def toggle_display_mode(self):
        """切换显示模式：智能排序 vs 时间排序"""
        self.smart_mode = not self.smart_mode

        if self.smart_mode:
            self.mode_toggle_btn.setText("🔄 切换到时间排序")
        else:
            self.mode_toggle_btn.setText("🔄 切换到智能排序")

        # 重新应用当前数据
        if hasattr(self, 'all_alarms') and self.all_alarms:
            original_alarms = self.all_alarms[:]  # 保存原始数据
            self.update_table_with_smart_pagination(original_alarms, self.current_display_count)

    def update_table_display(self, alarms):
        """显示告警数据到表格"""
        self.update_table(alarms)

    def update_table(self, alarms):
        """更新表格"""
        # 保存当前排序状态
        header = self.table.horizontalHeader()
        sort_column = header.sortIndicatorSection()
        sort_order = header.sortIndicatorOrder()

        # 关键优化：禁用排序和更新以大幅提升性能
        self.table.setSortingEnabled(False)  # 🚀 关闭排序，避免填充时反复重排
        self.table.setUpdatesEnabled(False)  # 🚀 关闭更新，减少闪烁

        # 清空现有数据，释放内存
        self.table.clearContents()
        self.table.setRowCount(len(alarms))

        for row, alarm in enumerate(alarms):
            try:
                # 创建表格项，添加Tooltip支持
                full_code_name = str(alarm.get('code_name', ''))
                full_me_name = str(alarm.get('me_name', ''))

                # 截断显示文本，但保留完整信息在Tooltip中
                short_code_name = full_code_name[:MAX_CODE_NAME_LENGTH] + "..." if len(full_code_name) > MAX_CODE_NAME_LENGTH else full_code_name
                short_me_name = full_me_name[:MAX_ME_NAME_LENGTH] + "..." if len(full_me_name) > MAX_ME_NAME_LENGTH else full_me_name

                # 构建表格项数组，对应完整的203列
                items = []

                # 基础显示列（0-9）
                items.extend([
                    QTableWidgetItem(str(alarm.get('status_marks', ''))),           # 0 - 状态
                    QTableWidgetItem(str(alarm.get('focus_marks', ''))),            # 1 - 重点标记（只显示🎯重点）
                    QTableWidgetItem(str(alarm.get('relation_marks', ''))),         # 2 - 关联标记（使用分析结果显示，例如🟡衍生←根源名）
                    QTableWidgetItem(short_code_name),                              # 3 - 告警名称
                    QTableWidgetItem(str(alarm.get('root_group_id_short', ''))),    # 4 - 根源ID（短）
                    QTableWidgetItem(str(alarm.get('severity', ''))),               # 5 - 级别
                    QTableWidgetItem(short_me_name),                                # 5 - 网元名称
                    QTableWidgetItem(str(alarm.get('ne_ip', ''))),                  # 6 - IP地址
                    QTableWidgetItem(str(alarm.get('time_str', ''))),               # 7 - 发生时间
                    QTableWidgetItem(str(alarm.get('duration_str', ''))),           # 8 - 考核持续时间
                    QTableWidgetItem(str(alarm.get('operator_info', ''))),          # 9 - 运营商
                ])

                # 数据库字段（10-28）
                items.extend([
                    QTableWidgetItem(str(alarm.get('ack_state_name', ''))),         # 10 - 确认状态
                    QTableWidgetItem(str(alarm.get('alarm_type_name', ''))),        # 11 - 告警类型
                    QTableWidgetItem(str(alarm.get('reason_name', ''))),            # 12 - 原因
                    QTableWidgetItem(str(alarm.get('additional_text', ''))),        # 13 - 附加信息
                    QTableWidgetItem(str(alarm.get('position_name', ''))),          # 14 - 位置信息
                    QTableWidgetItem(str(alarm.get('alarm_code', ''))),             # 15 - 告警代码
                    QTableWidgetItem(str(alarm.get('res_type_name', ''))),          # 16 - 资源类型
                    QTableWidgetItem(str(alarm.get('clear_state_name', ''))),       # 17 - 清除状态
                    QTableWidgetItem(str(alarm.get('ack_user_id', ''))),            # 18 - 确认用户
                    QTableWidgetItem(str(alarm.get('comment_text', ''))),           # 19 - 备注
                    QTableWidgetItem(str(alarm.get('first_seen_at', ''))),          # 20 - 首次发现
                    QTableWidgetItem(str(alarm.get('last_seen_at', ''))),           # 21 - 最后发现
                    QTableWidgetItem(str(alarm.get('alarm_key', ''))),              # 22 - 告警键
                    QTableWidgetItem(str(alarm.get('is_new', ''))),                 # 23 - 是否新告警
                    QTableWidgetItem(str(alarm.get('is_active', ''))),              # 24 - 是否活跃
                    QTableWidgetItem(str(alarm.get('status_changed_at', ''))),      # 25 - 状态变更时间
                    QTableWidgetItem(str(alarm.get('created_at', ''))),             # 26 - 创建时间
                    QTableWidgetItem(str(alarm.get('actual_duration_minutes', ''))), # 27 - 实际持续时间
                    QTableWidgetItem(str(alarm.get('is_baseline', ''))),            # 28 - 是否基线
                ])

                # 基础告警字段组（27-56）
                items.extend([
                    QTableWidgetItem(str(alarm.get('raw_s_nssai', ''))),            # 27 - S-NSSAI
                    QTableWidgetItem(str(alarm.get('raw_id', ''))),                 # 28 - 告警ID
                    QTableWidgetItem(str(alarm.get('raw_ackinfo', ''))),            # 29 - 确认信息
                    QTableWidgetItem(str(alarm.get('raw_ackstate', ''))),           # 30 - 确认状态码
                    QTableWidgetItem(str(alarm.get('raw_acksystemid', ''))),        # 31 - 确认系统ID
                    QTableWidgetItem(str(alarm.get('raw_acktime', ''))),            # 32 - 确认时间
                    QTableWidgetItem(str(alarm.get('raw_ackuserid', ''))),          # 33 - 确认用户ID
                    QTableWidgetItem(str(alarm.get('raw_admc', ''))),               # 34 - 管理域
                    QTableWidgetItem(str(alarm.get('raw_admcname', ''))),           # 35 - 管理域名称
                    QTableWidgetItem(str(alarm.get('raw_aid', ''))),                # 36 - AID
                    QTableWidgetItem(str(alarm.get('raw_alarmchangedtime', ''))),   # 37 - 告警变更时间
                    QTableWidgetItem(str(alarm.get('raw_alarmcode', ''))),          # 38 - 告警代码
                    QTableWidgetItem(str(alarm.get('raw_alarmkey', ''))),           # 39 - 告警键
                    QTableWidgetItem(str(alarm.get('raw_alarmraisedtime', ''))),    # 40 - 告警发生时间
                    QTableWidgetItem(str(alarm.get('raw_alarmsource', ''))),        # 41 - 告警源
                    QTableWidgetItem(str(alarm.get('raw_alarmtitle', ''))),         # 42 - 告警标题
                    QTableWidgetItem(str(alarm.get('raw_alarmtype', ''))),          # 43 - 告警类型码
                    QTableWidgetItem(str(alarm.get('raw_alarmtypename', ''))),      # 44 - 告警类型名
                    QTableWidgetItem(str(alarm.get('raw_auxiliarycount', ''))),     # 45 - 辅助计数
                    QTableWidgetItem(str(alarm.get('raw_clearstate', ''))),         # 46 - 清除状态码
                    QTableWidgetItem(str(alarm.get('raw_clearstatename', ''))),     # 47 - 清除状态名
                    QTableWidgetItem(str(alarm.get('raw_cleartypename', ''))),      # 48 - 清除类型名
                    QTableWidgetItem(str(alarm.get('raw_codename', ''))),           # 49 - 告警名称
                    QTableWidgetItem(str(alarm.get('raw_commentsystemid', ''))),    # 50 - 备注系统ID
                    QTableWidgetItem(str(alarm.get('raw_commenttext', ''))),        # 51 - 备注内容
                    QTableWidgetItem(str(alarm.get('raw_commenttime', ''))),        # 52 - 备注时间
                    QTableWidgetItem(str(alarm.get('raw_commentuserid', ''))),      # 53 - 备注用户ID
                    QTableWidgetItem(str(alarm.get('raw_componentdn', ''))),        # 54 - 组件DN
                    QTableWidgetItem(str(alarm.get('raw_dstsaving', ''))),          # 55 - 夏令时
                    QTableWidgetItem(str(alarm.get('raw_intermittencecount', ''))), # 56 - 间歇计数
                ])

                # 设备和位置字段组（57-86）
                items.extend([
                    QTableWidgetItem(str(alarm.get('raw_intermittenceduplicatedkey', ''))), # 57 - 间歇重复键
                    QTableWidgetItem(str(alarm.get('raw_link', ''))),               # 58 - 链接
                    QTableWidgetItem(str(alarm.get('raw_linkname', ''))),           # 59 - 链接名称
                    QTableWidgetItem(str(alarm.get('raw_maintainstatus', ''))),     # 60 - 维护状态
                    QTableWidgetItem(str(alarm.get('raw_me', ''))),                 # 61 - 网元
                    QTableWidgetItem(str(alarm.get('raw_mename', ''))),             # 62 - 网元名称
                    QTableWidgetItem(str(alarm.get('raw_moc', ''))),                # 63 - MOC
                    QTableWidgetItem(str(alarm.get('raw_mocname', ''))),            # 64 - MOC名称
                    QTableWidgetItem(str(alarm.get('raw_naffiltered', ''))),        # 65 - NAF过滤
                    QTableWidgetItem(str(alarm.get('raw_nbiid', ''))),              # 66 - NBI ID
                    QTableWidgetItem(str(alarm.get('raw_neip', ''))),               # 67 - 网元IP
                    QTableWidgetItem(str(alarm.get('raw_neplmns', ''))),            # 68 - 网元PLMN
                    QTableWidgetItem(str(alarm.get('raw_nmcreasoncode', ''))),      # 69 - NMC原因代码
                    QTableWidgetItem(str(alarm.get('raw_offsetalarmraisedtime', ''))), # 70 - 偏移告警时间
                    QTableWidgetItem(str(alarm.get('raw_operations', ''))),         # 71 - 操作列表
                    QTableWidgetItem(str(alarm.get('raw_parentinfo', ''))),         # 72 - 父级信息
                    QTableWidgetItem(str(alarm.get('raw_perceivedseverity', ''))),  # 73 - 感知严重级别
                    QTableWidgetItem(str(alarm.get('raw_perceivedseverityname', ''))), # 74 - 感知严重级别名
                    QTableWidgetItem(str(alarm.get('raw_plmndisplayinfo', ''))),    # 75 - PLMN显示信息
                    QTableWidgetItem(str(alarm.get('raw_plmns', ''))),              # 76 - PLMN列表
                    QTableWidgetItem(str(alarm.get('raw_position', ''))),           # 77 - 位置
                    QTableWidgetItem(str(alarm.get('raw_positionname', ''))),       # 78 - 位置名称
                    QTableWidgetItem(str(alarm.get('raw_productrestype', ''))),     # 79 - 产品资源类型
                    QTableWidgetItem(str(alarm.get('raw_ranshareswitch', ''))),     # 80 - RAN共享开关
                    QTableWidgetItem(str(alarm.get('raw_reasoncode', ''))),         # 81 - 原因代码
                    QTableWidgetItem(str(alarm.get('raw_reasonname', ''))),         # 82 - 原因名称
                    QTableWidgetItem(str(alarm.get('raw_relatedrules', ''))),       # 83 - 相关规则
                    QTableWidgetItem(str(alarm.get('raw_relatedruletype', ''))),    # 84 - 相关规则类型
                    QTableWidgetItem(str(alarm.get('raw_relationflag', ''))),       # 85 - 关联标志
                    QTableWidgetItem("(已移至第2列)"),                              # 86 - 关联标志名(已移至第2列)
                ])

                # 继续添加剩余的117列...
                items = self._add_remaining_table_items(items, alarm)

                # 新增：根源ID列（204，追加在最后）
                root_full = str(alarm.get('root_group_id', ''))
                root_short = root_full[:8] + '...' if len(root_full) > 8 else root_full
                items.append(QTableWidgetItem(root_short))  # 204 - 根源ID（短）

                # 设置Tooltip
                items[2].setToolTip(full_code_name)  # 告警名称
                items[4].setToolTip(full_me_name)    # 网元名称
                if root_full:
                    items[-1].setToolTip(root_full)

                # 设置行颜色
                relationflag = alarm.get('relationflag', 0)
                if relationflag == 1:  # 根源告警
                    color = QColor(*COLOR_ROOT_ALARM)
                elif relationflag == 2:  # 衍生告警
                    color = QColor(*COLOR_DERIVED_ALARM)
                elif any(keyword in str(alarm.get('code_name', '')) for keyword in self.focus_keywords):  # 重点关注
                    color = QColor(*COLOR_FOCUS_ALARM)
                elif alarm.get('is_new'):  # 新告警
                    color = QColor(*COLOR_NEW_ALARM)
                else:
                    color = QColor(*COLOR_NORMAL)

                # 🚀 性能优化：只为可见列设置数据和样式
                for col_index, item in enumerate(items):
                    if col_index < self.table.columnCount():
                        # 跳过隐藏列，避免不必要的对象创建和样式设置
                        if self.table.isColumnHidden(col_index):
                            continue

                        item.setBackground(color)

                        # 设置文字颜色为黑色，确保可见
                        item.setForeground(QColor(0, 0, 0))  # 黑色文字

                        # 设置字体（新告警加粗）
                        if alarm.get('is_new', 0):
                            font = item.font()
                            font.setBold(True)
                            item.setFont(font)

                        self.table.setItem(row, col_index, item)
            except Exception:
                # 跳过有问题的行
                continue

        # 🚀 恢复排序和更新（关键性能优化）
        self.table.setUpdatesEnabled(True)   # 先恢复更新
        self.table.setSortingEnabled(True)   # 再恢复排序功能

        # 恢复之前的排序状态
        if sort_column >= 0:
            self.table.sortItems(sort_column, sort_order)

        # 🔧 调试：检查NBI ID列的数据
        if alarms and len(alarms) > 0:
            nbi_id_col = 66
            sample_alarm = alarms[0]
            nbi_id_value = sample_alarm.get('raw_nbiid', '')
            self.add_log(f"🔍 NBI ID调试 - 第一条告警的NBI ID值: '{nbi_id_value}' (类型: {type(nbi_id_value)})")

            # 检查表格中的实际显示值
            if self.table.rowCount() > 0 and nbi_id_col < self.table.columnCount():
                item = self.table.item(0, nbi_id_col)
                if item:
                    display_value = item.text()
                    self.add_log(f"🔍 NBI ID调试 - 表格显示值: '{display_value}'")
                else:
                    self.add_log("🔍 NBI ID调试 - 表格项为空")

        # 搜索过滤功能已删除

    def _add_remaining_table_items(self, items, alarm):
        """添加剩余的117列表格项"""

        # RAN专用字段组（87-116）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_relationresult', ''))),     # 87 - 关联结果
            QTableWidgetItem(str(alarm.get('raw_respath', ''))),            # 88 - 资源路径
            QTableWidgetItem(str(alarm.get('raw_restype', ''))),            # 89 - 资源类型
            QTableWidgetItem(str(alarm.get('raw_restypename', ''))),        # 90 - 资源类型名
            QTableWidgetItem(str(alarm.get('raw_rootcount', ''))),          # 91 - 根源计数
            QTableWidgetItem(str(alarm.get('raw_sequence', ''))),           # 92 - 序列号
            QTableWidgetItem(str(alarm.get('raw_servertime', ''))),         # 93 - 服务器时间
            QTableWidgetItem(str(alarm.get('raw_timezoneid', ''))),         # 94 - 时区ID
            QTableWidgetItem(str(alarm.get('raw_timezoneoffset', ''))),     # 95 - 时区偏移
            QTableWidgetItem(str(alarm.get('raw_visible', ''))),            # 96 - 可见性
            QTableWidgetItem(str(alarm.get('raw_ran_ems_fm_additional_params', ''))), # 97 - RAN EMS参数
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_board_type', ''))), # 98 - RAN告警板类型
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_dn', ''))),    # 99 - RAN告警DN
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_location', ''))), # 100 - RAN告警位置
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_object', ''))), # 101 - RAN告警对象
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_object_id', ''))), # 102 - RAN告警对象ID
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_object_name', ''))), # 103 - RAN告警对象名
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_object_type', ''))), # 104 - RAN告警对象类型
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_service_id', ''))), # 105 - RAN告警服务ID
            QTableWidgetItem(str(alarm.get('raw_ran_fm_alarm_site_name', ''))), # 106 - RAN告警站点名
            QTableWidgetItem(str(alarm.get('raw_ran_fm_ne_virtualization', ''))), # 107 - RAN网元虚拟化
            QTableWidgetItem(str(alarm.get('raw_ran_sdr_fm_native_param', ''))), # 108 - RAN SDR参数
        ])

        # 诊断相关字段组（109-138）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisresultstatus', ''))), # 109 - AAX诊断结果状态
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisstatus', ''))), # 110 - AAX诊断状态
            QTableWidgetItem(str(alarm.get('raw_aax_lastdiagnosistime', ''))), # 111 - AAX最后诊断时间
            QTableWidgetItem(str(alarm.get('raw_aax_unrelationflag', ''))), # 112 - AAX非关联标志
        ])

        # 嵌套字段：S-NSSAI详细信息（113-118）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_s_nssai_columnname', ''))), # 113 - S-NSSAI列名
            QTableWidgetItem(str(alarm.get('raw_s_nssai_datatype', ''))),   # 114 - S-NSSAI数据类型
            QTableWidgetItem(str(alarm.get('raw_s_nssai_displayname', ''))), # 115 - S-NSSAI显示名
            QTableWidgetItem(str(alarm.get('raw_s_nssai_extentionfield', ''))), # 116 - S-NSSAI扩展字段
            QTableWidgetItem(str(alarm.get('raw_s_nssai_value', ''))),      # 117 - S-NSSAI值
        ])

        # 嵌套字段：AAX诊断结果状态详细信息（118-123）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisresultstatus_columnname', ''))), # 118
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisresultstatus_datatype', ''))), # 119
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisresultstatus_displayname', ''))), # 120
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisresultstatus_extentionfield', ''))), # 121
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisresultstatus_value', ''))), # 122
        ])

        # 嵌套字段：AAX诊断状态详细信息（123-128）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisstatus_columnname', ''))), # 123
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisstatus_datatype', ''))), # 124
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisstatus_displayname', ''))), # 125
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisstatus_extentionfield', ''))), # 126
            QTableWidgetItem(str(alarm.get('raw_aax_diagnosisstatus_value', ''))), # 127
        ])

        # 嵌套字段：AAX最后诊断时间详细信息（128-133）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_aax_lastdiagnosistime_columnname', ''))), # 128
            QTableWidgetItem(str(alarm.get('raw_aax_lastdiagnosistime_datatype', ''))), # 129
            QTableWidgetItem(str(alarm.get('raw_aax_lastdiagnosistime_displayname', ''))), # 130
            QTableWidgetItem(str(alarm.get('raw_aax_lastdiagnosistime_extentionfield', ''))), # 131
            QTableWidgetItem(str(alarm.get('raw_aax_lastdiagnosistime_value', ''))), # 132
        ])

        # 嵌套字段：AAX非关联标志详细信息（133-138）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_aax_unrelationflag_columnname', ''))), # 133
            QTableWidgetItem(str(alarm.get('raw_aax_unrelationflag_datatype', ''))), # 134
            QTableWidgetItem(str(alarm.get('raw_aax_unrelationflag_displayname', ''))), # 135
            QTableWidgetItem(str(alarm.get('raw_aax_unrelationflag_extentionfield', ''))), # 136
            QTableWidgetItem(str(alarm.get('raw_aax_unrelationflag_value', ''))), # 137
        ])

        # 嵌套字段：告警标题详细信息（138-143）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_alarmtitle_columnname', ''))), # 138
            QTableWidgetItem(str(alarm.get('raw_alarmtitle_datatype', ''))), # 139
            QTableWidgetItem(str(alarm.get('raw_alarmtitle_displayname', ''))), # 140
            QTableWidgetItem(str(alarm.get('raw_alarmtitle_extentionfield', ''))), # 141
            QTableWidgetItem(str(alarm.get('raw_alarmtitle_value', ''))), # 142
        ])

        # 嵌套字段：维护状态详细信息（143-148）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_maintainstatus_columnname', ''))), # 143
            QTableWidgetItem(str(alarm.get('raw_maintainstatus_datatype', ''))), # 144
            QTableWidgetItem(str(alarm.get('raw_maintainstatus_displayname', ''))), # 145
            QTableWidgetItem(str(alarm.get('raw_maintainstatus_extentionfield', ''))), # 146
            QTableWidgetItem(str(alarm.get('raw_maintainstatus_value', ''))), # 147
        ])

        # 嵌套字段：网元PLMN详细信息（148-150）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_neplmns_mcc', ''))), # 148
            QTableWidgetItem(str(alarm.get('raw_neplmns_mnc', ''))), # 149
        ])

        # 继续添加剩余的53列...
        self._add_final_table_items(items, alarm)

        return items

    def _add_final_table_items(self, items, alarm):
        """添加最后的53列表格项"""

        # 嵌套字段：父级信息详细信息（150-155）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_parentinfo_relation', ''))), # 150
            QTableWidgetItem(str(alarm.get('raw_parentinfo_relation_2025_04', ''))), # 151
            QTableWidgetItem(str(alarm.get('raw_parentinfo_relation_2025_06', ''))), # 152
            QTableWidgetItem(str(alarm.get('raw_parentinfo_relation_2025_07', ''))), # 153
            QTableWidgetItem(str(alarm.get('raw_parentinfo_relation_2025_08', ''))), # 154
        ])

        # 嵌套字段：PLMN列表详细信息（155-157）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_plmns_mcc', ''))), # 155
            QTableWidgetItem(str(alarm.get('raw_plmns_mnc', ''))), # 156
        ])

        # 嵌套字段：产品资源类型详细信息（157-162）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_productrestype_columnname', ''))), # 157
            QTableWidgetItem(str(alarm.get('raw_productrestype_datatype', ''))), # 158
            QTableWidgetItem(str(alarm.get('raw_productrestype_displayname', ''))), # 159
            QTableWidgetItem(str(alarm.get('raw_productrestype_extentionfield', ''))), # 160
            QTableWidgetItem(str(alarm.get('raw_productrestype_value', ''))), # 161
        ])

        # 嵌套字段：RAN EMS参数详细信息（162-167）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_ems_params_columnname', ''))), # 162
            QTableWidgetItem(str(alarm.get('raw_ran_ems_params_datatype', ''))), # 163
            QTableWidgetItem(str(alarm.get('raw_ran_ems_params_displayname', ''))), # 164
            QTableWidgetItem(str(alarm.get('raw_ran_ems_params_extentionfield', ''))), # 165
            QTableWidgetItem(str(alarm.get('raw_ran_ems_params_value', ''))), # 166
        ])

        # 嵌套字段：RAN告警板类型详细信息（167-172）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_board_type_columnname', ''))), # 167
            QTableWidgetItem(str(alarm.get('raw_ran_board_type_datatype', ''))), # 168
            QTableWidgetItem(str(alarm.get('raw_ran_board_type_displayname', ''))), # 169
            QTableWidgetItem(str(alarm.get('raw_ran_board_type_extentionfield', ''))), # 170
            QTableWidgetItem(str(alarm.get('raw_ran_board_type_value', ''))), # 171
        ])

        # 嵌套字段：RAN告警DN详细信息（172-177）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_dn_columnname', ''))), # 172
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_dn_datatype', ''))), # 173
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_dn_displayname', ''))), # 174
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_dn_extentionfield', ''))), # 175
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_dn_value', ''))), # 176
        ])

        # 嵌套字段：RAN告警位置详细信息（177-182）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_location_columnname', ''))), # 177
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_location_datatype', ''))), # 178
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_location_displayname', ''))), # 179
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_location_extentionfield', ''))), # 180
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_location_value', ''))), # 181
        ])

        # 嵌套字段：RAN告警对象详细信息（182-187）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_columnname', ''))), # 182
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_datatype', ''))), # 183
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_displayname', ''))), # 184
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_extentionfield', ''))), # 185
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_value', ''))), # 186
        ])

        # 嵌套字段：RAN告警对象ID详细信息（187-192）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_id_columnname', ''))), # 187
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_id_datatype', ''))), # 188
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_id_displayname', ''))), # 189
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_id_extentionfield', ''))), # 190
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_id_value', ''))), # 191
        ])

        # 嵌套字段：RAN告警对象名详细信息（192-197）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_name_columnname', ''))), # 192
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_name_datatype', ''))), # 193
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_name_displayname', ''))), # 194
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_name_extentionfield', ''))), # 195
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_name_value', ''))), # 196
        ])

        # 嵌套字段：RAN告警对象类型详细信息（197-202）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_type_columnname', ''))), # 197
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_type_datatype', ''))), # 198
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_type_displayname', ''))), # 199
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_type_extentionfield', ''))), # 200
            QTableWidgetItem(str(alarm.get('raw_ran_alarm_object_type_value', ''))), # 201
        ])

        # 最后的字段（202-203）
        items.extend([
            QTableWidgetItem(str(alarm.get('raw_relatedruletype_null', ''))), # 202
            QTableWidgetItem(str(alarm.get('raw_relationresult_value', ''))), # 203
        ])

    def on_item_double_clicked(self, item):
        """双击表格项"""
        row = item.row()
        if row < len(self.alarms_data):
            alarm_data = self.alarms_data[row]
            dialog = AlarmDetailDialog(alarm_data, self)
            dialog.exec()

    def closeEvent(self, event):
        """关闭事件 - 简化清理逻辑，避免阻塞"""
        self.add_log("🔄 正在关闭程序...")

        # 保存列宽设置
        try:
            self.save_column_widths()
        except Exception:
            pass

        # 停止所有定时器
        self.add_log("⏹️ 停止所有定时器...")
        for timer_name in ['timer', 'monitor_timer', 'data_timer', 'local_timer', 'fetch_timer']:
            if hasattr(self, timer_name):
                try:
                    getattr(self, timer_name).stop()
                except Exception:
                    pass

        # 停止数据加载线程 - 简化逻辑
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            self.add_log("⏹️ 正在停止数据加载线程...")
            try:
                self.worker.stop()
                if not self.worker.wait(1000):  # 只等待1秒
                    self.worker.terminate()
            except Exception:
                pass

        # 停止网管抓取线程 - 简化逻辑，避免复杂的异步清理
        if hasattr(self, 'fetch_worker') and self.fetch_worker and self.fetch_worker.isRunning():
            self.add_log("⏹️ 正在停止网管抓取线程...")
            try:
                self.fetch_worker.stop()
                if not self.fetch_worker.wait(1000):  # 只等待1秒
                    self.fetch_worker.terminate()
            except Exception:
                pass

        self.add_log("✅ 程序关闭")
        event.accept()

    def fetch_from_web(self):
        """从网管系统获取最新数据 - 使用内置获取逻辑"""
        try:
            # 获取配置
            config = configparser.ConfigParser()
            if not os.path.exists(CONFIG_FILE):
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self, "提示", "请先配置用户名和密码")
                self.show_config()
                return

            config.read(CONFIG_FILE, encoding='utf-8')
            username = config.get('login', 'username', fallback='')
            password = config.get('login', 'password', fallback='')

            if not username or not password:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(self, "提示", "请先配置用户名和密码")
                self.show_config()
                return

            # 显示进度
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)

            # 停止之前的获取线程（如果存在）
            if (hasattr(self, 'fetch_worker') and
                self.fetch_worker is not None and
                self.fetch_worker.isRunning()):
                self.fetch_worker.stop()
                self.fetch_worker.wait(2000)

            # 启动网管数据获取线程 - 使用完全内置的抓取逻辑
            self.fetch_worker = WebFetchWorker(username, password, self.db_file, headless=True)
            self.fetch_worker.finished_ok.connect(self.on_fetch_success)
            self.fetch_worker.finished_err.connect(self.on_fetch_error)
            self.fetch_worker.log_message.connect(self.add_log)
            self.fetch_worker.progress_update.connect(self.on_fetch_progress)
            self.fetch_worker.finished.connect(self.on_fetch_finished)
            self.fetch_worker.start()

        except Exception as e:
            self.add_log(f"❌ 启动网管获取失败: {e}")
            self.progress_bar.setVisible(False)

    def on_fetch_progress(self, percentage, description):
        """更新获取进度"""
        self.progress_bar.setValue(percentage)
        self.status_bar.showMessage(f"正在获取数据: {description}")

    def on_fetch_success(self, stats):
        """网管数据获取成功"""
        # 显示详细的统计信息
        current_count = stats.get('current_active_count', 0)
        new_count = stats.get('new_count', 0)
        continuing_count = stats.get('continuing_count', 0)
        cleared_count = stats.get('cleared_count', 0)
        is_first_fetch = stats.get('is_first_fetch', False)

        if is_first_fetch:
            self.add_log(f"🎯 首次抓取建立基线: {current_count} 条活跃告警")
            self.add_log("📊 基线建立完成，下次抓取将开始统计状态变化")
        else:
            self.add_log(f"🎉 同步完成: 网管当前{current_count}条活跃告警")
            if new_count > 0:
                self.add_log(f"🆕 新出现: {new_count} 条告警")
            if cleared_count > 0:
                self.add_log(f"❌ 已消失: {cleared_count} 条告警")
            self.add_log(f"📍 持续存在: {continuing_count} 条告警")

        self.add_log("🔄 自动刷新本地数据...")
        # 自动刷新显示最新数据
        self.refresh_data()

    def on_fetch_error(self, error_msg):
        """网管数据获取失败"""
        self.add_log(f"❌ 网管数据获取失败: {error_msg}")
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.warning(self, "获取失败", error_msg)

    def on_fetch_finished(self):
        """网管数据获取完成（无论成功失败）"""
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("就绪")

    def show_config(self):
        """显示配置对话框"""
        dialog = ConfigDialog(self)
        if dialog.exec() == QDialog.Accepted:
            dialog.save_config()

            # 应用定时器配置
            enabled, interval = dialog.get_timer_config()
            old_enabled = self.fetch_timer_enabled
            old_interval = self.fetch_interval_minutes

            self.fetch_timer_enabled = enabled
            self.fetch_interval_minutes = interval

            # 更新定时器状态
            if enabled and not old_enabled:
                # 启用定时器
                self.start_fetch_timer()
            elif not enabled and old_enabled:
                # 禁用定时器
                self.stop_fetch_timer()
            elif enabled and old_enabled and interval != old_interval:
                # 间隔时间改变，重启定时器
                self.stop_fetch_timer()
                self.start_fetch_timer()

            # 重新加载重点关键字
            self.focus_keywords = self.load_focus_keywords()
            self.add_log(f"🎯 重点关键字已更新: {', '.join(self.focus_keywords)}")

            self.add_log("⚙️ 配置已保存并应用")



    # 搜索功能已删除

    def load_timer_config(self):
        """加载定时器配置"""
        try:
            config = configparser.ConfigParser()
            if os.path.exists(CONFIG_FILE):
                config.read(CONFIG_FILE, encoding='utf-8')

                # 加载定时器设置
                self.fetch_timer_enabled = config.getboolean('timer', 'enabled', fallback=False)
                self.fetch_interval_minutes = config.getint('timer', 'interval_minutes', fallback=10)

                # 如果启用了定时器，启动它
                if self.fetch_timer_enabled:
                    self.start_fetch_timer()
                    self.add_log(f"⏰ 定时获取已启用，间隔: {self.fetch_interval_minutes} 分钟")

                # 更新按钮文本
                self.update_timer_button_text()
        except Exception as e:
            self.add_log(f"❌ 加载定时器配置失败: {e}")

    def start_fetch_timer(self):
        """启动定时获取定时器"""
        if self.fetch_timer_enabled and self.fetch_interval_minutes > 0:
            interval_ms = self.fetch_interval_minutes * 60 * 1000  # 转换为毫秒
            self.fetch_timer.start(interval_ms)
            self.add_log(f"⏰ 定时获取定时器已启动，间隔: {self.fetch_interval_minutes} 分钟")

    def stop_fetch_timer(self):
        """停止定时获取定时器"""
        if self.fetch_timer.isActive():
            self.fetch_timer.stop()
            self.add_log("⏰ 定时获取定时器已停止")

    def auto_fetch_from_web(self):
        """自动从网管获取数据"""
        # 🕐 检查时间限制（独立模块）
        try:
            is_restricted, next_allowed = self.TimeLimitManager.is_time_restricted()
            if is_restricted:
                if next_allowed:
                    self.add_log(f"🕐 当前处于时间限制段，跳过定时获取。下次允许时间: {next_allowed.strftime('%H:%M')}")
                else:
                    self.add_log("🕐 当前处于时间限制段，跳过定时获取")
                return
        except Exception as e:
            # 时间限制模块异常不影响正常获取
            self.add_log(f"⚠️ 时间限制检查异常: {e}，继续执行获取")

        # 检查是否有获取任务正在进行
        if (hasattr(self, 'fetch_worker') and
            self.fetch_worker is not None and
            self.fetch_worker.isRunning()):
            self.add_log("⚠️ 上次获取任务还未完成，跳过本次定时获取")
            return

        self.add_log("⏰ 定时获取触发，开始从网管系统获取数据...")
        self.fetch_from_web()

    def toggle_fetch_timer(self):
        """切换定时获取状态"""
        if self.fetch_timer_enabled and self.fetch_timer.isActive():
            # 当前启用，禁用它
            self.fetch_timer_enabled = False
            self.stop_fetch_timer()
            self.timer_btn.setText("⏰ 启用定时获取")
            self.add_log("⏰ 定时获取已禁用")
        else:
            # 当前禁用，启用它
            if self.fetch_interval_minutes <= 0:
                self.fetch_interval_minutes = 10  # 默认10分钟

            self.fetch_timer_enabled = True
            self.start_fetch_timer()
            self.timer_btn.setText("⏸️ 禁用定时获取")
            self.add_log(f"⏰ 定时获取已启用，间隔: {self.fetch_interval_minutes} 分钟")

        # 保存配置
        self.save_timer_config()

    def save_timer_config(self):
        """保存定时器配置到文件"""
        try:
            config = configparser.ConfigParser()

            # 读取现有配置
            if os.path.exists(CONFIG_FILE):
                config.read(CONFIG_FILE, encoding='utf-8')

            # 确保timer section存在
            if not config.has_section('timer'):
                config.add_section('timer')

            # 更新定时器配置
            config.set('timer', 'enabled', str(self.fetch_timer_enabled))
            config.set('timer', 'interval_minutes', str(self.fetch_interval_minutes))

            # 保存配置
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                config.write(f)

        except Exception as e:
            self.add_log(f"❌ 保存定时器配置失败: {e}")

    def update_timer_button_text(self):
        """更新定时器按钮文本"""
        if self.fetch_timer_enabled and self.fetch_timer.isActive():
            self.timer_btn.setText("⏸️ 禁用定时获取")
        else:
            self.timer_btn.setText("⏰ 启用定时获取")








def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("告警监控系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("告警监控")

    # 创建主窗口
    window = AlarmMonitorGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
